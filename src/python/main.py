from fastapi import Fast<PERSON><PERSON>, UploadFile, File, HTTPException, Request, Body
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Optional, Dict, Tuple
import os
import json
import shutil
import tempfile
import logging
import ffmpeg
import soundfile as sf
import numpy as np
import librosa
import cv2
import mediapipe as mp
import math
import torch
from PIL import Image
from torchvision import transforms
import google.generativeai as genai
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
import re
import warnings
import asyncio
from contextlib import contextmanager, nullcontext

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Configure Gemini API
genai.configure(api_key=os.getenv("GEMINI_API_KEY"))

# Initialize FastAPI app
app = FastAPI(
    title="Mockly API",
    description="Combined API for speech-to-text, video analysis, speech analysis, and question generation",
    version="1.0.0"
)

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Temporary directory for file processing
TEMP_DIR = Path("temp")
TEMP_DIR.mkdir(exist_ok=True)

# Video analysis model directory
VIDEO_ANALYSIS_DIR = Path(__file__).parent

# Suppress warnings
warnings.simplefilter("ignore", UserWarning)

# Initialize emotion analysis model variables
pth_backbone_model = None
pth_LSTM_model = None

# Device configuration for video analysis
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
torch.backends.cudnn.benchmark = True  # Enable CUDNN benchmarking for better performance
print(f"Using device for video analysis: {device}")
if torch.cuda.is_available():
    print(f"CUDA Device: {torch.cuda.get_device_name(0)}")
    print(f"CUDA Version: {torch.version.cuda}")

# Constants
DICT_EMO = {0: 'Neutral', 1: 'Happiness', 2: 'Sadness', 3: 'Surprise',
            4: 'Fear', 5: 'Disgust', 6: 'Anger'}

# Initialize Whisper model (on CPU)
try:
    model_id = "openai/whisper-large-v3"

    from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor, pipeline

    model = AutoModelForSpeechSeq2Seq.from_pretrained(
        model_id,
        torch_dtype=torch.float32,
        low_cpu_mem_usage=True,
        use_safetensors=True,
    )

    processor = AutoProcessor.from_pretrained(model_id)
    whisper_model = pipeline(
        "automatic-speech-recognition",
        model=model,
        tokenizer=processor.tokenizer,
        feature_extractor=processor.feature_extractor,
        torch_dtype=torch.float32,
        chunk_length_s=30,         # Process in 30-second chunks
        stride_length_s=5,         # 5-second overlap between chunks
        return_timestamps=True,    # Enable timestamp tokens for longer audio
    )
    logger.info("Whisper model loaded successfully on CPU")
except Exception as e:
    logger.error(f"Failed to load Whisper model: {e}")
    raise RuntimeError("Whisper model initialization failed")

# Load emotion analysis models (on GPU)
try:
    # Model names
    name_backbone_model = '0_66_49_wo_gl'
    name_LSTM_model = 'SAVEE'

    # Model paths
    backbone_path = VIDEO_ANALYSIS_DIR / f'torchscript_model_{name_backbone_model}.pth'
    lstm_path = VIDEO_ANALYSIS_DIR / f'{name_LSTM_model}.pth'

    logger.info(f"Looking for backbone model at: {backbone_path}")
    logger.info(f"Looking for LSTM model at: {lstm_path}")

    # Check if model files exist
    if backbone_path.exists() and lstm_path.exists():
        # Load models with CUDA optimization
        pth_backbone_model = torch.jit.load(str(backbone_path)).to(device)
        pth_LSTM_model = torch.jit.load(str(lstm_path)).to(device)

        # Enable CUDA optimizations if using GPU
        if device == 'cuda':
            pth_backbone_model = torch.jit.optimize_for_inference(pth_backbone_model)
            pth_LSTM_model = torch.jit.optimize_for_inference(pth_LSTM_model)

        # Set models to evaluation mode
        pth_backbone_model.eval()
        pth_LSTM_model.eval()

        logger.info(f"Emotion analysis models loaded successfully on {device}")
    else:
        logger.warning("Emotion analysis model files not found. Using fallback random predictions.")
except Exception as e:
    logger.error(f"Failed to load emotion analysis models: {e}")

# Pydantic Models
class AnalyzeRequest(BaseModel):
    job_profile: str
    question: str
    answer: str
    experience_level: Optional[str] = "Not specified"
    required_skills: Optional[List[str]] = []

class TechnicalQuestionRequest(BaseModel):
    job_profile: str
    experience_level: str  # e.g., "Entry", "Mid", "Senior"
    required_skills: List[str]  # e.g., ["Python", "React", "SQL"]
    num_questions: Optional[int] = 15  # Default to 15 questions

class BehavioralQuestionRequest(BaseModel):
    job_profile: str  # e.g., "Software Engineer"
    experience_level: str  # e.g., "Entry", "Mid", "Senior"
    focus_areas: Optional[List[str]] = None  # e.g., ["leadership", "teamwork"]
    num_questions: Optional[int] = 5  # Default to 5 questions

class EmotionAnalysis(BaseModel):
    emotion: str
    frequency: float
    average_score: float
    confidence_level: str

class ConfidenceCategory(BaseModel):
    percentage: float
    emotions: List[Tuple[str, float]]

class VideoAnalysisResponse(BaseModel):
    success: bool
    emotion_analysis: List[EmotionAnalysis]
    dominant_emotion: EmotionAnalysis
    confidence_analysis: Dict[str, ConfidenceCategory]
    dominant_confidence: str
    confidence_summary: str
    analysis_summary: str

# Speech-to-Text Helper Functions
def extract_audio(video_path: str, audio_path: str) -> None:
    """Extract audio from video to WAV (16kHz, mono)."""
    try:
        # Input video stream
        stream = ffmpeg.input(video_path)

        # Extract audio with specific settings for better quality
        stream = ffmpeg.output(
            stream,
            audio_path,
            acodec="pcm_s16le",  # 16-bit PCM audio
            ar="16000",          # 16kHz sample rate
            ac=1,                # mono audio
            audio_bitrate="192k" # higher bitrate for better quality
        )

        # Run ffmpeg with overwrite and suppress output
        ffmpeg.run(stream, overwrite_output=True, quiet=True)
        logger.info(f"Audio extracted to {audio_path}")

        # Verify the audio file was created
        if not Path(audio_path).exists():
            raise Exception("Audio extraction failed - output file not created")

    except ffmpeg.Error as e:
        logger.error(f"FFmpeg error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to extract audio from video")
    except Exception as e:
        logger.error(f"Audio extraction error: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process audio")

def clean_audio(audio_path: str, cleaned_path: str) -> None:
    """Clean audio to reduce noise using noisereduce if available, fallback to librosa."""
    try:
        # Try to use noisereduce for better noise reduction
        try:
            import noisereduce as nr
            # Load audio
            audio, sr = sf.read(audio_path)

            # Apply noisereduce (spectral gating)
            reduced_noise = nr.reduce_noise(
                y=audio,
                sr=sr,
                stationary=False,  # Dynamic noise (e.g., typing, fans)
                prop_decrease=0.75,  # Moderate reduction to preserve speech
            )

            # Save cleaned audio
            sf.write(cleaned_path, reduced_noise, sr)
            logger.info(f"Audio cleaned with noisereduce to {cleaned_path}")
        except ImportError:
            # Fallback to librosa method
            logger.warning("noisereduce not available, falling back to basic noise reduction")
            audio, sr = librosa.load(audio_path, sr=16000)

            # Basic noise reduction: spectral subtraction
            noise_clip = audio[:int(0.5 * sr)]
            noise_mag = np.mean(np.abs(librosa.stft(noise_clip)))

            # Compute STFT of full audio
            stft = librosa.stft(audio)
            mag, phase = np.abs(stft), np.angle(stft)

            # Subtract noise magnitude
            mag_clean = np.maximum(mag - noise_mag, 0)

            # Reconstruct audio
            stft_clean = mag_clean * np.exp(1j * phase)
            audio_clean = librosa.istft(stft_clean)

            # Save cleaned audio
            sf.write(cleaned_path, audio_clean, sr)
            logger.info(f"Audio cleaned with basic method to {cleaned_path}")
    except Exception as e:
        logger.error(f"Cleaning error: {e}")
        raise HTTPException(status_code=500, detail="Failed to clean audio")

def transcribe_audio(audio_path: str) -> str:
    """Transcribe audio using Whisper model (on CPU)."""
    try:
        # Load audio
        audio, sr = sf.read(audio_path)
        if sr != 16000:
            logger.warning(f"Audio sample rate is {sr}, expected 16000. Resampling may affect quality.")

        # Process on CPU with return_timestamps=True for longer audio
        result = whisper_model(
            {"raw": audio, "sampling_rate": sr}, 
            return_timestamps=True,  # Enable timestamp tokens for longer audio
            chunk_length_s=30,       # Process in 30-second chunks
            batch_size=1             # Lower batch size to reduce memory usage
        )
        
        transcript = result["text"].strip()
        logger.info("Transcription completed")
        return transcript
    except Exception as e:
        logger.error(f"Transcription error: {e}")
        raise HTTPException(status_code=500, detail="Failed to transcribe audio")

# Video Analysis Helper Functions
def pth_processing(fp):
    class PreprocessInput(torch.nn.Module):
        def __init__(self):
            super(PreprocessInput, self).__init__()

        def forward(self, x):
            x = x.to(torch.float32)
            x = torch.flip(x, dims=(0,))
            x[0, :, :] -= 91.4953
            x[1, :, :] -= 103.8827
            x[2, :, :] -= 131.0912
            return x

    def get_img_torch(img):
        ttransform = transforms.Compose([
            transforms.PILToTensor(),
            PreprocessInput()
        ])
        img = img.resize((224, 224), Image.Resampling.NEAREST)
        img = ttransform(img).to(device)
        img = torch.unsqueeze(img, 0)
        return img
    return get_img_torch(fp)

def norm_coordinates(normalized_x, normalized_y, image_width, image_height):
    x_px = min(math.floor(normalized_x * image_width), image_width - 1)
    y_px = min(math.floor(normalized_y * image_height), image_height - 1)
    return x_px, y_px

def get_box(fl, w, h):
    idx_to_coors = {}
    for idx, landmark in enumerate(fl.landmark):
        landmark_px = norm_coordinates(landmark.x, landmark.y, w, h)
        if landmark_px:
            idx_to_coors[idx] = landmark_px

    x_min = np.min(np.asarray(list(idx_to_coors.values()))[:,0])
    y_min = np.min(np.asarray(list(idx_to_coors.values()))[:,1])
    endX = np.max(np.asarray(list(idx_to_coors.values()))[:,0])
    endY = np.max(np.asarray(list(idx_to_coors.values()))[:,1])

    (startX, startY) = (max(0, x_min), max(0, y_min))
    (endX, endY) = (min(w - 1, endX), min(h - 1, endY))

    return startX, startY, endX, endY

def analyze_emotion_sequence(emotion_scores, window_size=15):
    if len(emotion_scores) < window_size:
        return None, 0

    recent_scores = emotion_scores[-window_size:]
    emotion_stats = {}

    for emotion, score in recent_scores:
        if emotion not in emotion_stats:
            emotion_stats[emotion] = {'count': 1, 'total_score': score}
        else:
            emotion_stats[emotion]['count'] += 1
            emotion_stats[emotion]['total_score'] += score

    dominant_emotion = max(emotion_stats.items(), key=lambda x: x[1]['count'])

    if dominant_emotion:
        emotion_name = dominant_emotion[0]
        stats = dominant_emotion[1]
        dominance_ratio = stats['count'] / window_size
        avg_confidence = stats['total_score'] / stats['count']

        confidence_boost = 1.0
        if dominance_ratio > 0.7:
            confidence_boost = 1.3
        elif dominance_ratio > 0.5:
            confidence_boost = 1.2

        return emotion_name, min(avg_confidence * confidence_boost, 1.0)

    return None, 0

def get_confidence_level(score, emotion_name, frequency=None):
    if emotion_name == 'Neutral' and frequency is not None and frequency > 0.75:
        return "Average"

    if emotion_name in ['Happiness', 'Neutral']:
        if score >= 0.7: return "High"
        elif score >= 0.5: return "Average"
        else: return "Low"

    elif emotion_name in ['Surprise', 'Sadness']:
        if score >= 0.6: return "High"
        elif score >= 0.4: return "Average"
        else: return "Low"

    else:
        if score >= 0.5: return "High"
        elif score >= 0.3: return "Average"
        else: return "Low"

def analyze_confidence_dominance(frame_emotions, total_frames):
    confidence_categories = {
        'High': {'count': 0, 'emotions': [], 'percentage': 0},
        'Average': {'count': 0, 'emotions': [], 'percentage': 0},
        'Low': {'count': 0, 'emotions': [], 'percentage': 0}
    }

    frame_counts = {'high': 0, 'average': 0, 'low': 0}

    if 'Neutral' in frame_emotions:
        neutral_data = frame_emotions['Neutral']
        neutral_count = len(neutral_data)
        neutral_frequency = neutral_count / total_frames

        if neutral_frequency > 0.75:
            frame_counts['average'] += neutral_count
            confidence_categories['Average']['emotions'].append(('Neutral', neutral_frequency))
        else:
            if sum(neutral_data) / neutral_count >= 0.4:
                frame_counts['high'] += neutral_count
                confidence_categories['High']['emotions'].append(('Neutral', neutral_frequency))

    emotion_groups = {
        'high': ['Happiness'],
        'average': ['Surprise', 'Sadness'],
        'low': ['Anger', 'Fear', 'Disgust']
    }

    for confidence, emotions in emotion_groups.items():
        for emotion in emotions:
            if emotion in frame_emotions:
                scores = frame_emotions[emotion]
                count = len(scores)
                frequency = count / total_frames

                if confidence == 'low':
                    frame_counts['low'] += count
                    confidence_categories['Low']['emotions'].append((emotion, frequency))
                else:
                    avg_score = sum(scores) / count
                    if avg_score >= 0.4:
                        frame_counts[confidence] += count
                        confidence_categories[confidence.title()]['emotions'].append((emotion, frequency))

    total_processed_frames = sum(frame_counts.values())
    if total_processed_frames > 0:
        for category, count in zip(['High', 'Average', 'Low'],
                                 [frame_counts['high'], frame_counts['average'], frame_counts['low']]):
            confidence_categories[category]['percentage'] = (count / total_processed_frames) * 100

    dominant_confidence = max(confidence_categories.items(),
                            key=lambda x: x[1]['percentage'])

    return confidence_categories, dominant_confidence[0]

def generate_confidence_summary(confidence_stats: Dict, dominant_confidence: str) -> Tuple[str, str]:
    summary = []
    if dominant_confidence == 'High':
        summary.append("Video shows predominantly high-confidence emotions (Happiness/Neutral)")
    elif dominant_confidence == 'Average':
        summary.append("Video shows predominantly average-confidence emotions (Surprise/Sadness)")
    else:
        summary.append("Video shows predominantly low-confidence emotions")

    for confidence_level in ['High', 'Average', 'Low']:
        if confidence_stats[confidence_level]['emotions']:
            emotions = [f"{emotion}: {freq*100:.1f}%"
                       for emotion, freq in confidence_stats[confidence_level]['emotions']]
            summary.append(f"{confidence_level} confidence emotions: {', '.join(emotions)}")

    # Generate detailed analysis summary
    if dominant_confidence == 'High':
        analysis_summary = "The candidate demonstrated exceptional emotional expressiveness with clear and consistent display of positive emotions. Their facial expressions showed strong confidence and authenticity, particularly during moments of happiness and neutral interactions. This suggests a high level of emotional intelligence and ability to maintain composed, positive engagement throughout the video."
    elif dominant_confidence == 'Average':
        analysis_summary = "The candidate showed moderate emotional expressiveness with a balanced display of emotions. Their facial expressions indicated a comfortable level of engagement, though with some variations in intensity. The presence of both surprise and sadness suggests genuine emotional responses, while maintaining appropriate professional composure."
    else:
        analysis_summary = "The candidate's emotional expressions showed some areas for improvement. The presence of low-confidence emotions suggests potential discomfort or uncertainty. Consider providing feedback on maintaining more positive and confident expressions during professional interactions."

    return "\n".join(summary), analysis_summary

# Speech Analysis Helper Functions
def calculate_speech_metrics(answer: str) -> dict:
    """Calculate basic speech metrics from the answer."""
    words = answer.split()
    word_count = len(words)

    # Assume average speaking rate of 130 words per minute
    estimated_speech_rate = 130
    speech_duration_minutes = word_count / estimated_speech_rate if word_count > 0 else 1

    # Count word repetitions (only significant words > 3 chars)
    word_freq = {}
    repeated_words = []
    for word in words:
        word = word.lower()
        if len(word) > 3:
            word_freq[word] = word_freq.get(word, 0) + 1
            if word_freq[word] > 2:
                repeated_words.append(word)

    return {
        "word_count": word_count,
        "speech_rate": round(word_count / speech_duration_minutes),
        "repeated_words": list(set(repeated_words))
    }

def create_gemini_model():
    """Create and configure Gemini model for analysis."""
    generation_config = {
        "temperature": 0.7,
        "top_p": 0.95,
        "top_k": 40,
        "max_output_tokens": 8192,
        "response_mime_type": "text/plain",
    }

    return genai.GenerativeModel(
        model_name="gemini-1.5-pro",
        generation_config=generation_config,
        system_instruction="""You are an expert interview assessor specialized in evaluating candidates across various job profiles.
You must always respond with valid JSON only. Do not include any text before or after the JSON object.
Analyze the provided job profile, question, and answer, then respond in the following specific JSON format:

{
    "domain_knowledge": {
        "what_went_well": ["point 1", "point 2"],
        "what_could_be_better": ["improvement 1", "improvement 2"],
        "missing_terminologies": ["term1", "term2"]
    },
    "articulation": {
        "what_went_well": ["point 1", "point 2"],
        "what_could_be_better": ["improvement 1", "improvement 2"]
    },
    "communication": {
        "grammar": {"error_count": number, "details": "description of errors if any"},
        "filler_words": {"count": number, "words": ["word1", "word2"]},
        "speech_rate": {"words_per_minute": number, "assessment": "Good/Too Fast/Too Slow"},
        "word_repetition": {"count": number, "repeated_words": ["word1", "word2"]},
        "intonation": {"assessment": "Good/Monotonous/Variable", "details": "description of intonation"}
    },
    "recommended_response": {
        "key_points": ["point 1", "point 2"],
        "structured_answer": "complete recommended answer",
        "improvement_suggestions": ["suggestion 1", "suggestion 2"]
    },
    "overall_assessment": {
        "technical_score": number (1-10),
        "communication_score": number (1-10),
        "overall_score": number (1-10),
        "summary": "brief overall assessment",
        "hiring_recommendation": "Strong Hire/Hire/Consider/Do Not Hire"
    }
}"""
    )

# Question Generation Helper Functions
async def generate_questions(prompt: str, num_questions: int, question_type: str) -> List[str]:
    """Generate interview questions using Gemini."""
    try:
        model = genai.GenerativeModel("gemini-1.5-flash")
        response = model.generate_content(prompt)

        # Clean and parse response
        text = response.text.strip()
        lines = text.split("\n")
        questions = []

        for line in lines:
            line = line.strip()
            # Remove common prefixes (e.g., "1.", "- ", "* ")
            cleaned = re.sub(r'^\d+\.\s*|^[-\*]\s*', '', line)
            if cleaned and cleaned.endswith("?"):
                questions.append(cleaned)

        # Limit to requested number
        questions = questions[:num_questions]

        # If fewer questions than requested, generate more with a follow-up prompt
        while len(questions) < num_questions:
            remaining = num_questions - len(questions)
            follow_up_prompt = prompt.replace(
                f"Generate {num_questions}",
                f"Generate {remaining} additional"
            )
            response = model.generate_content(follow_up_prompt)
            text = response.text.strip()
            lines = text.split("\n")

            for line in lines:
                line = line.strip()
                cleaned = re.sub(r'^\d+\.\s*|^[-\*]\s*', '', line)
                if cleaned and cleaned.endswith("?") and len(questions) < num_questions:
                    questions.append(cleaned)

        # Format with question numbers
        return [f"Question {i+1}: {q}" for i, q in enumerate(questions)]
    except Exception as e:
        # Log error for debugging
        print(f"Gemini API error: {str(e)}")
        # Fallback to sample questions
        sample_questions = {
            "technical": [
                "How do you optimize a database query for performance?",
                "Explain the difference between REST and GraphQL APIs.",
                "How would you handle rate limiting in a Node.js API?",
                "What are the benefits of using TypeScript in a React project?",
                "Describe how to implement authentication in a web app.",
                "How do you manage state in a large-scale React application?",
                "What is event-driven architecture, and how is it used in Node.js?",
                "How would you design a caching strategy for a web app?",
                "Explain the concept of middleware in Express.",
                "How do you ensure security in a RESTful API?",
                "What are React hooks, and how do they improve development?",
                "How do you handle file uploads in Node.js?",
                "Explain the purpose of indexes in a database.",
                "How would you debug a memory leak in a Node.js app?",
                "What is lazy loading in React, and when is it useful?"
            ],
            "behavioral": [
                "Describe a time you resolved a conflict in a team.",
                "How do you prioritize tasks under tight deadlines?",
                "Tell me about about a time you learned a new skill quickly.",
                "What do you do when a teammate disagrees with you?",
                "How have you handled a challenging project in the past?"
            ]
        }
        return [f"Question {i+1}: {q}" for i, q in enumerate(sample_questions[question_type][:num_questions])]

# API Endpoints

# Speech-to-Text Endpoint
@app.post("/speech-to-text")
async def speech_to_text(file: UploadFile = File(...)):
    """Process uploaded video: extract audio, clean noise, transcribe to text.
    Returns JSON with transcript.
    """
    # Validate file type
    if not file.filename.lower().endswith((".mp4", ".webm")):
        logger.warning(f"Invalid file type: {file.filename}")
        raise HTTPException(status_code=400, detail="Only MP4 and WebM files are supported")

    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix, dir=TEMP_DIR) as temp_video:
        video_path = temp_video.name
        audio_path = str(TEMP_DIR / f"{Path(video_path).stem}.wav")
        cleaned_path = str(TEMP_DIR / f"{Path(video_path).stem}_cleaned.wav")

        try:
            # Save uploaded video
            content = await file.read()
            with open(video_path, "wb") as f:
                f.write(content)
            logger.info(f"Video uploaded: {video_path}")

            # Step 1: Extract audio (run in thread pool to not block)
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, extract_audio, video_path, audio_path)

            # Step 2: Clean audio (run in thread pool)
            await loop.run_in_executor(None, clean_audio, audio_path, cleaned_path)

            # Step 3: Transcribe (run in thread pool)
            transcript = await loop.run_in_executor(None, transcribe_audio, cleaned_path)

            # Return transcript
            return JSONResponse(content={"transcript": transcript})

        except HTTPException as e:
            raise e
        except Exception as e:
            logger.error(f"Processing error: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")

        finally:
            # Clean up temporary files
            for path in [video_path, audio_path, cleaned_path]:
                if Path(path).exists():
                    try:
                        Path(path).unlink()
                        logger.info(f"Deleted temp file: {path}")
                    except Exception as e:
                        logger.warning(f"Failed to delete {path}: {e}")

# Legacy Speech-to-Text Endpoint (for backward compatibility)
@app.post("/process-video")
async def process_video(file: UploadFile = File(...)):
    """Legacy endpoint for backward compatibility.
    Redirects to the speech-to-text endpoint.
    """
    logger.info("Legacy process-video endpoint called, using speech-to-text implementation")
    return await speech_to_text(file)


# Video Analysis Endpoint
@app.post("/analyze-video", response_model=VideoAnalysisResponse)
async def analyze_video(video: UploadFile = File(...)):
    """Analyze video for emotions and confidence."""
    if not video.filename.lower().endswith(('.mp4', '.avi', '.mov', '.webm')):
        raise HTTPException(status_code=400, detail="Unsupported file format. Please upload MP4, AVI, MOV, or WebM file.")

    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(video.filename).suffix) as temp_file:
        try:
            # Save uploaded video
            content = await video.read()
            temp_file.write(content)
            temp_file.flush()

            # Convert WebM to MP4 if needed
            video_path = temp_file.name
            if video.filename.lower().endswith('.webm'):
                mp4_path = str(TEMP_DIR / f"{Path(video_path).stem}.mp4")
                try:
                    # Convert WebM to MP4 using ffmpeg
                    stream = ffmpeg.input(video_path)
                    stream = ffmpeg.output(stream, mp4_path, acodec='aac', vcodec='h264')
                    ffmpeg.run(stream, overwrite_output=True, quiet=True)
                    video_path = mp4_path
                except ffmpeg.Error as e:
                    logger.error(f"FFmpeg conversion error: {e}")
                    raise HTTPException(status_code=500, detail="Failed to process video format")

            # Open video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                raise HTTPException(status_code=400, detail="Failed to open video file")

            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # Initialize face mesh
            mp_face_mesh = mp.solutions.face_mesh
            lstm_features = []
            emotion_history = []
            frame_emotions = {}

            # Pre-allocate CUDA tensors if using GPU
            if device == 'cuda':
                torch.cuda.empty_cache()  # Clear GPU memory
                lstm_buffer = torch.zeros((1, 10, 512), device=device)  # Adjust size based on your model

            with mp_face_mesh.FaceMesh(
                max_num_faces=1,
                refine_landmarks=False,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5) as face_mesh:

                frame_batch = []
                batch_size = 16 if device == 'cuda' else 1

                while cap.isOpened():
                    success, frame = cap.read()
                    if not success:
                        break

                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    results = face_mesh.process(frame_rgb)

                    if results.multi_face_landmarks:
                        for fl in results.multi_face_landmarks:
                            h, w = frame.shape[:2]
                            startX, startY, endX, endY = get_box(fl, w, h)
                            cur_face = frame_rgb[startY:endY, startX:endX]

                            if cur_face.size == 0:
                                continue

                            # Process face for emotion detection
                            try:
                                cur_face = pth_processing(Image.fromarray(cur_face))
                                frame_batch.append(cur_face)

                                if len(frame_batch) >= batch_size:
                                    # Process batch
                                    with torch.cuda.amp.autocast() if device == 'cuda' else nullcontext():
                                        batch_tensor = torch.cat(frame_batch, dim=0)
                                        with torch.no_grad():
                                            features = torch.nn.functional.relu(pth_backbone_model.extract_features(batch_tensor))

                                        # Update LSTM features
                                        for feat in features:
                                            if len(lstm_features) == 0:
                                                lstm_features = [feat.cpu().numpy()]*10
                                            else:
                                                lstm_features = lstm_features[1:] + [feat.cpu().numpy()]

                                            if device == 'cuda':
                                                lstm_f = torch.from_numpy(np.vstack(lstm_features)).to(device)
                                                lstm_f = lstm_f.unsqueeze(0)
                                            else:
                                                lstm_f = torch.from_numpy(np.vstack(lstm_features)).to(device)
                                                lstm_f = torch.unsqueeze(lstm_f, 0)

                                            with torch.no_grad():
                                                output = pth_LSTM_model(lstm_f).cpu().numpy()

                                            cl = np.argmax(output)
                                            confidence_score = float(output[0][cl])
                                            emotion_name = DICT_EMO[cl]

                                            emotion_history.append((emotion_name, confidence_score))

                                            if emotion_name not in frame_emotions:
                                                frame_emotions[emotion_name] = []
                                            frame_emotions[emotion_name].append(confidence_score)

                                    frame_batch = []

                            except Exception as e:
                                logger.error(f"Error processing face: {e}")
                                continue

                # Process remaining frames in batch
                if frame_batch:
                    with torch.cuda.amp.autocast() if device == 'cuda' else nullcontext():
                        batch_tensor = torch.cat(frame_batch, dim=0)
                        with torch.no_grad():
                            features = torch.nn.functional.relu(pth_backbone_model.extract_features(batch_tensor))

                        for feat in features:
                            if len(lstm_features) == 0:
                                lstm_features = [feat.cpu().numpy()]*10
                            else:
                                lstm_features = lstm_features[1:] + [feat.cpu().numpy()]

                            if device == 'cuda':
                                lstm_f = torch.from_numpy(np.vstack(lstm_features)).to(device)
                                lstm_f = lstm_f.unsqueeze(0)
                            else:
                                lstm_f = torch.from_numpy(np.vstack(lstm_features)).to(device)
                                lstm_f = torch.unsqueeze(lstm_f, 0)

                            with torch.no_grad():
                                output = pth_LSTM_model(lstm_f).cpu().numpy()

                            cl = np.argmax(output)
                            confidence_score = float(output[0][cl])
                            emotion_name = DICT_EMO[cl]

                            emotion_history.append((emotion_name, confidence_score))

                            if emotion_name not in frame_emotions:
                                frame_emotions[emotion_name] = []
                            frame_emotions[emotion_name].append(confidence_score)

            cap.release()

            if not frame_emotions:
                raise HTTPException(status_code=400, detail="No faces detected in the video")

            # Prepare analysis results
            emotion_analysis = []
            for emotion in frame_emotions:
                if frame_emotions[emotion]:
                    count = len(frame_emotions[emotion])
                    avg_score = sum(frame_emotions[emotion]) / count
                    frequency = count / total_frames
                    adjusted_score = avg_score * (1 + frequency)
                    confidence_level = get_confidence_level(adjusted_score, emotion, frequency)

                    emotion_analysis.append(EmotionAnalysis(
                        emotion=emotion,
                        frequency=frequency,
                        average_score=avg_score,
                        confidence_level=confidence_level
                    ))

            # Get dominant emotion
            dominant_emotion = max(frame_emotions.items(), key=lambda x: len(x[1]))
            dom_emotion_name = dominant_emotion[0]
            dom_emotion_freq = len(dominant_emotion[1]) / total_frames
            dom_emotion_score = sum(dominant_emotion[1]) / len(dominant_emotion[1])

            # Get confidence analysis
            confidence_stats, dominant_confidence = analyze_confidence_dominance(frame_emotions, total_frames)

            # Generate confidence summary
            confidence_summary, analysis_summary = generate_confidence_summary(confidence_stats, dominant_confidence)

            return VideoAnalysisResponse(
                success=True,
                emotion_analysis=emotion_analysis,
                dominant_emotion=EmotionAnalysis(
                    emotion=dom_emotion_name,
                    frequency=dom_emotion_freq,
                    average_score=dom_emotion_score,
                    confidence_level=get_confidence_level(
                        dom_emotion_score * (1 + dom_emotion_freq),
                        dom_emotion_name,
                        dom_emotion_freq
                    )
                ),
                confidence_analysis=confidence_stats,
                dominant_confidence=dominant_confidence,
                confidence_summary=confidence_summary,
                analysis_summary=analysis_summary
            )

        except Exception as e:
            logger.error(f"Video analysis error: {e}")
            raise HTTPException(status_code=500, detail=str(e))
        finally:
            try:
                os.unlink(temp_file.name)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")
            if device == 'cuda':
                torch.cuda.empty_cache()

# Speech Analysis Endpoint
@app.post("/analyze")
async def analyze_response(request: AnalyzeRequest):
    """Analyze a candidate's interview response."""
    # Extract request data
    job_profile = request.job_profile
    question = request.question
    answer = request.answer
    experience_level = request.experience_level
    role_specific_skills = request.required_skills

    # Calculate speech metrics
    speech_metrics = calculate_speech_metrics(answer)

    # Construct input for Gemini
    input_text = f"""
Job Profile: {job_profile}
Experience Level: {experience_level}
Required Skills: {', '.join(role_specific_skills) if role_specific_skills else 'Not specified'}

Question: {question}
Answer: {answer}

Additional Metrics:
- Word Count: {speech_metrics['word_count']}
- Speech Rate: {speech_metrics['speech_rate']} words/minute
- Repeated Words: {', '.join(speech_metrics['repeated_words']) if speech_metrics['repeated_words'] else 'None'}

Analyze this interview response considering the job profile and provide a detailed assessment.
Return your response as a valid JSON object. Do not include any additional text before or after the JSON.
"""

    # Generate analysis using Gemini
    model = create_gemini_model()
    try:
        response = model.generate_content(input_text)
        cleaned_response = response.text.strip()
        # Remove markdown code blocks if present
        if cleaned_response.startswith('```json'):
            cleaned_response = cleaned_response[7:]
        if cleaned_response.endswith('```'):
            cleaned_response = cleaned_response[:-3]
        cleaned_response = cleaned_response.strip()

        analysis = json.loads(cleaned_response)

        # Add speech metrics to communication section
        if 'communication' in analysis:
            analysis['communication']['speech_metrics'] = speech_metrics

    except json.JSONDecodeError as e:
        # Fallback analysis on JSON parsing error
        analysis = {
            "error_details": {
                "original_error": str(e),
                "raw_response": response.text[:500] + "..." if len(response.text) > 500 else response.text
            },
            "domain_knowledge": {
                "what_went_well": ["Unable to analyze"],
                "what_could_be_better": ["Analysis failed"],
                "missing_terminologies": []
            },
            "articulation": {
                "what_went_well": ["Unable to analyze"],
                "what_could_be_better": ["Analysis failed"]
            },
            "communication": {
                "grammar": {"error_count": 0, "details": "Not analyzed"},
                "filler_words": {"count": 0, "words": []},
                "speech_rate": {"words_per_minute": speech_metrics['speech_rate'], "assessment": "Not analyzed"},
                "word_repetition": {"count": len(speech_metrics['repeated_words']), "repeated_words": speech_metrics['repeated_words']},
                "intonation": {"assessment": "Not analyzed", "details": "Not analyzed"},
                "speech_metrics": speech_metrics
            },
            "recommended_response": {
                "key_points": [],
                "structured_answer": "Analysis failed",
                "improvement_suggestions": ["Retry the request"]
            },
            "overall_assessment": {
                "technical_score": 0,
                "communication_score": 0,
                "overall_score": 0,
                "summary": "Analysis failed - please try again",
                "hiring_recommendation": "Unable to determine"
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

    # Construct response
    response_data = {
        "status": "success",
        "timestamp": datetime.now().isoformat(),
        "metadata": {
            "job_profile": job_profile,
            "experience_level": experience_level,
            "question_length": len(question),
            "answer_length": len(answer),
            "analysis_version": "1.2"
        },
        "original_input": {
            "job_profile": job_profile,
            "question": question,
            "answer": answer,
            "required_skills": role_specific_skills
        },
        "analysis": analysis
    }

    return response_data

# Question Generation Endpoints
@app.post("/generate-technical-questions", response_model=List[str])
async def generate_technical_questions(request: TechnicalQuestionRequest):
    """Generate technical interview questions."""
    if not request.job_profile or not request.required_skills:
        raise HTTPException(status_code=400, detail="Job profile and required skills are required")

    if request.experience_level not in ["Entry", "Mid", "Senior"]:
        raise HTTPException(status_code=400, detail="Invalid experience level")

    num_questions = max(5, min(20, request.num_questions))

    prompt = f"""
    Generate exactly {num_questions} technical interview questions for a {request.experience_level}-level {request.job_profile}.
    The candidate must have expertise in these skills: {', '.join(request.required_skills)}.
    Questions should be specific, relevant, and tailored to the experience level.
    Return only the questions, one per line, without numbers, bullet points, or additional text.
    Each question must end with a question mark.
    """

    return await generate_questions(prompt, num_questions, "technical")

@app.post("/generate-behavioral-questions", response_model=List[str])
async def generate_behavioral_questions(request: BehavioralQuestionRequest):
    """Generate behavioral interview questions."""
    if not request.job_profile:
        raise HTTPException(status_code=400, detail="Job profile is required")

    if request.experience_level not in ["Entry", "Mid", "Senior"]:
        raise HTTPException(status_code=400, detail="Invalid experience level")

    num_questions = max(5, min(10, request.num_questions))

    focus_areas_text = (
        f"Focus on these areas: {', '.join(request.focus_areas)}."
        if request.focus_areas
        else ""
    )

    prompt = f"""
    Generate exactly {num_questions} behavioral interview questions for a {request.experience_level}-level {request.job_profile}.
    {focus_areas_text}
    Questions should assess soft skills, teamwork, problem-solving, leadership, and cultural fit.
    Return only the questions, one per line, without numbers, bullet points, or additional text.
    Each question must end with a question mark.
    """

    return await generate_questions(prompt, num_questions, "behavioral")

@app.post("/generate-interview-questions")
async def generate_interview_questions(
    technical_request: TechnicalQuestionRequest = Body(...),
    behavioral_request: Optional[BehavioralQuestionRequest] = Body(None)
):
    """Generate both technical and behavioral interview questions."""
    technical_questions = await generate_technical_questions(technical_request)
    behavioral_questions = (
        await generate_behavioral_questions(behavioral_request)
        if behavioral_request
        else []
    )

    # Return in the format expected by the frontend
    return {
        "technical_questions": [q.split(': ', 1)[1] if ': ' in q else q for q in technical_questions],
        "behavioral_questions": [q.split(': ', 1)[1] if ': ' in q else q for q in behavioral_questions]
    }

# Health Check Endpoints
@app.get("/")
async def root():
    """Root endpoint."""
    return {"message": "Mockly API is running"}

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "version": "1.0.0",
        "features": [
            "Speech-to-Text",
            "Video Analysis",
            "Speech Analysis",
            "Question Generation"
        ],
        "endpoints": {
            "speech_to_text": "/speech-to-text",
            "legacy_speech_to_text": "/process-video",
            "video_analysis": "/analyze-video",
            "speech_analysis": "/analyze",
            "technical_questions": "/generate-technical-questions",
            "behavioral_questions": "/generate-behavioral-questions",
            "all_questions": "/generate-interview-questions"
        }
    }

@app.get("/check-models")
async def check_models():
    """Check if emotion detection models are available."""
    backbone_path = VIDEO_ANALYSIS_DIR / f'torchscript_model_0_66_49_wo_gl.pth'
    lstm_path = VIDEO_ANALYSIS_DIR / 'SAVEE.pth'

    return {
        "backbone_model": {
            "path": str(backbone_path),
            "exists": backbone_path.exists(),
            "loaded": pth_backbone_model is not None
        },
        "lstm_model": {
            "path": str(lstm_path),
            "exists": lstm_path.exists(),
            "loaded": pth_LSTM_model is not None
        }
    }

# Cleanup on shutdown
@app.on_event("shutdown")
async def shutdown_event():
    """Clean up temp directory on shutdown."""
    if TEMP_DIR.exists():
        shutil.rmtree(TEMP_DIR, ignore_errors=True)
        logger.info("Temp directory cleaned up")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)


