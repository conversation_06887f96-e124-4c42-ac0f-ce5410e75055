/* Add these styles to your globals.css file */

/* React Select Custom Styling */
.react-select-container .react-select__control {
  @apply bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm hover:border-gray-400 dark:hover:border-gray-600;
}

.react-select-container .react-select__control--is-focused {
  @apply border-primary-500 dark:border-primary-500 shadow-none hover:border-primary-500 dark:hover:border-primary-500 ring-1 ring-primary-500;
  box-shadow: 0 0 0 1px var(--color-primary-500);
}

.react-select-container .react-select__menu {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg;
}

.react-select-container .react-select__option {
  @apply text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700;
}

.react-select-container .react-select__option--is-selected {
  @apply bg-primary-500 text-white;
}

.react-select-container .react-select__multi-value {
  @apply bg-primary-100 dark:bg-primary-900 rounded;
}

.react-select-container .react-select__multi-value__label {
  @apply text-primary-800 dark:text-primary-200;
}

.react-select-container .react-select__multi-value__remove {
  @apply text-primary-500 dark:text-primary-400 hover:bg-primary-200 dark:hover:bg-primary-800 hover:text-primary-700 dark:hover:text-primary-200 rounded-r;
}

.react-select-container .react-select__input {
  @apply dark:text-white;
}

.react-select-container .react-select__placeholder {
  @apply text-gray-500 dark:text-gray-400;
}

.react-select-container .react-select__single-value {
  @apply text-gray-800 dark:text-gray-200;
}