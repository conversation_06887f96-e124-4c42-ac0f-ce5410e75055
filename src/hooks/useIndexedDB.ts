import { useEffect, useState } from 'react';

// Simple hook to interact with IndexedDB
export function useIndexedDB<T>(
  dbName: string,
  storeName: string,
  version = 1
) {
  const [db, setDb] = useState<IDBDatabase | null>(null);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const request = indexedDB.open(dbName, version);

    request.onerror = (event) => {
      setError(new Error('Failed to open IndexedDB'));
      console.error('IndexedDB error:', event);
    };

    request.onsuccess = (event) => {
      setDb((event.target as IDBOpenDBRequest).result);
    };

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains(storeName)) {
        db.createObjectStore(storeName, { keyPath: 'id' });
      }
    };

    return () => {
      if (db) {
        db.close();
      }
    };
  }, [dbName, storeName, version]);

  // Add an item to the store
  const addItem = async (item: T & { id: string }): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = db.transaction(storeName, 'readwrite');
      const store = transaction.objectStore(storeName);
      const request = store.put(item);

      request.onsuccess = () => resolve();
      request.onerror = () => reject(new Error('Failed to add item to IndexedDB'));
    });
  };

  // Get an item from the store
  const getItem = async (id: string): Promise<T | undefined> => {
    return new Promise((resolve, reject) => {
      if (!db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.get(id);

      request.onsuccess = () => resolve(request.result as T);
      request.onerror = () => reject(new Error('Failed to get item from IndexedDB'));
    });
  };

  // Get all items from the store
  const getAllItems = async (): Promise<T[]> => {
    return new Promise((resolve, reject) => {
      if (!db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const transaction = db.transaction(storeName, 'readonly');
      const store = transaction.objectStore(storeName);
      const request = store.getAll();

      request.onsuccess = () => resolve(request.result as T[]);
      request.onerror = () => reject(new Error('Failed to get items from IndexedDB'));
    });
  };

  return { db, error, addItem, getItem, getAllItems };
}