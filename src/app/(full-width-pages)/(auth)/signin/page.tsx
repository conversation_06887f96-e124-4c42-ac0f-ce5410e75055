import { getServerSession } from "next-auth";
import { redirect } from "next/navigation";
import SignInForm from "@/components/auth/SignInForm";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Sign In | Mockly",
  description: "Sign in to your account",
};

export default async function SignIn({
  searchParams,
}: {
  searchParams: { callbackUrl?: string };
}) {
  const session = await getServerSession(authOptions);
  const callbackUrl = searchParams?.callbackUrl || "/";

  if (session) {
    redirect(callbackUrl);
  }

  return <SignInForm callbackUrl={callbackUrl} />;
}
