'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import GridShape from '@/components/common/GridShape';

export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams?.get('error');

  const getErrorMessage = (error: string) => {
    switch (error) {
      case 'AccessDenied':
        return 'Access denied. Please check your credentials and try again.';
      case 'CredentialsSignin':
        return 'Invalid email or password. Please check your credentials and try again.';
      case 'OAuthSignin':
      case 'OAuthCallback':
        return 'There was a problem signing in with your social account. Please try again.';
      case 'OAuthCreateAccount':
        return 'Unable to create an account with this provider. Please try a different method.';
      case 'EmailCreateAccount':
        return 'An account with this email already exists. Please sign in instead.';
      case 'Callback':
        return 'There was a problem during authentication. Please try again.';
      case 'DatabaseError':
        return 'There was a problem connecting to the database. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  };

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen p-6 overflow-hidden z-1">
      <GridShape />
      <div className="mx-auto w-full max-w-[472px] text-center">
        <h1 className="mb-4 text-2xl font-bold text-gray-800 dark:text-white/90">
          Authentication Error
        </h1>
        
        <div className="mb-8 p-4 bg-red-50 dark:bg-red-900/10 rounded-lg">
          <p className="text-red-600 dark:text-red-400">
            {getErrorMessage(error || '')}
          </p>
        </div>

        <div className="flex flex-col gap-4">
          <Link
            href="/signin"
            className="inline-flex items-center justify-center rounded-lg border border-gray-300 bg-white px-5 py-3.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200"
          >
            Back to Sign In
          </Link>
          
          <Link
            href="/signup"
            className="inline-flex items-center justify-center rounded-lg border border-gray-300 bg-white px-5 py-3.5 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200"
          >
            Create an Account
          </Link>
        </div>
      </div>
    </div>
  );
}



