import type { Metadata } from "next";
import {
  ClockIcon,
  CodeBracketIcon,
  CubeIcon
} from "@heroicons/react/24/outline";
import RecentSubmissions from "@/components/dashboard/RecentSubmissions";

export const metadata: Metadata = {
  title: "Mockly Dashboard",
  description: "Mockly Dashboard",
};

interface MetricCardProps {
  icon: React.ReactNode;
  title: string;
  value: number;
}

const MetricCard = ({ icon, title, value }: MetricCardProps) => (
  <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
    <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-xl dark:bg-gray-800">
      {icon}
    </div>
    <div className="mt-5">
      <span className="text-sm text-gray-500 dark:text-gray-400">
        {title}
      </span>
      <h4 className="mt-2 font-bold text-gray-800 text-title-sm dark:text-white/90">
        {value}
      </h4>
    </div>
  </div>
);

export default function Dashboard() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <MetricCard
          icon={<ClockIcon className="text-gray-800 size-6 dark:text-white/90" />}
          title="Total Sessions Completed"
          value={24}
        />
        <MetricCard
          icon={<CodeBracketIcon className="text-gray-800 size-6 dark:text-white/90" />}
          title="Coding Problems Solved"
          value={156}
        />
        <MetricCard
          icon={<CubeIcon className="text-gray-800 size-6 dark:text-white/90" />}
          title="Total Custom Kits Created"
          value={8}
        />
      </div>

      <RecentSubmissions />
    </div>
  );
}