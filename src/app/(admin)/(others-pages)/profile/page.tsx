import UserInfoCard from "@/components/user-profile/UserInfoCard";
import UserMetaCard from "@/components/user-profile/UserMetaCard";
import UserEducationCard from "@/components/user-profile/UserEducationCard";
import UserExperienceCard from "@/components/user-profile/UserExperienceCard";
import UserProjectsCard from "@/components/user-profile/UserProjectsCard";
import { Metadata } from "next";
import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import User from "@/models/user";
import { connectToDatabase } from "@/lib/db"; // Ensure this path is correct

export const metadata: Metadata = {
  title: "User Profile",
  description: "User profile page",
};

export default async function Profile() {
  // Get the current session
  const session = await getServerSession(authOptions);

  // Connect to database and fetch user data
  await connectToDatabase();
  interface UserData {
    userAvatar?: string;
    designation?: string;
    location?: string;
    bio?: string;
    name?: string;
    firstName?: string;
    lastName?: string;
    email?: string | null;
    image?: string;
    id?: string;
    provider?: string;
    role?: string;
    socialLinks?: {
      facebook?: string;
      twitter?: string;
      linkedin?: string;
      instagram?: string;
    };
  }

  const userData: UserData | null = session?.user?.email
    ? await User.findOne({ email: session.user.email }).lean<UserData>().then((data) => ({
        userAvatar: data?.userAvatar,
        designation: data?.designation,
        location: data?.location,
        bio: data?.bio,
      }))
    : null;

  // Merge session data with database data
  const user: UserData = {
    ...session?.user,
    ...userData,
    // Ensure image/avatar is set with fallback to default
    image: userData?.userAvatar || session?.user?.image || "/default-avatar.png",
    // Include these fields from database
    designation: userData?.designation || "Team Manager",
    location: userData?.location || "Arizona, United States",
    bio: userData?.bio || "Team Manager",
    // Ensure name is a string or undefined, not null
    name: session?.user?.name || undefined
  };

  return (
    <div>
      <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
        <h3 className="mb-5 text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-7">
          My Profile
        </h3>
        <div className="space-y-6">
          <UserMetaCard userData={user} />
          <UserInfoCard userData={user} />
          <UserEducationCard userData={user} />
          <UserExperienceCard userData={user} />
          <UserProjectsCard userData={user} />
        </div>
      </div>
    </div>
  );
}
