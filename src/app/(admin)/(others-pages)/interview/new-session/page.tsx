import NewSessionForm from '@/components/interview/NewSessionForm';
import Card from '@/components/ui/card';

export default function NewInterviewSession() {
  return (
    <div className="space-y-8 w-full px-4">
      <div className="flex items-center justify-between max-w-7xl mx-auto w-full">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent">
            New Interview Session
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Create a new interview session to practice and improve your skills
          </p>
        </div>
      </div>

      <Card className="overflow-hidden border border-gray-200 dark:border-gray-700 shadow-lg hover:shadow-xl transition-shadow duration-300 max-w-7xl mx-auto">
        <div className="p-8 space-y-8 bg-white dark:bg-gray-800/95 backdrop-blur-sm rounded-lg">
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Session Details
            </h2>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Fill in the details below to start your interview session
            </p>
          </div>
          <NewSessionForm />
        </div>
      </Card>
    </div>
  );
}
