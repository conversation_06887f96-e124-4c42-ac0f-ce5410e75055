import Link from "next/link";
import React from "react";

export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center h-screen bg-gray-950 text-center px-4">
      <div className="rounded-lg bg-gray-900 border border-gray-800 p-8 max-w-md">
        <div className="flex justify-center mb-6">
          <div className="text-[120px] font-bold text-blue-500 leading-none">
            4<span className="text-blue-400">0</span>4
          </div>
        </div>
        
        <h2 className="text-2xl font-bold text-white mb-4">Page Not Found</h2>
        <p className="text-gray-300 mb-8">
          We can't seem to find the page you are looking for!
        </p>
        
        <Link
          href="/"
          className="inline-flex items-center justify-center rounded-md bg-blue-600 px-5 py-2.5 text-sm font-medium text-white hover:bg-blue-700 transition-colors"
        >
          Back to Home Page
        </Link>
      </div>
      
      <p className="mt-8 text-sm text-gray-500">
        &copy; {new Date().getFullYear()} - Mockly
      </p>
    </div>
  );
}
