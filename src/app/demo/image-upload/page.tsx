"use client";

import React, { useState } from "react";
import PageBreadcrumb from "@/components/common/PageBreadCrumb";
import ImageUploader from "@/components/form/form-elements/ImageUploader";
import Image from "next/image";

export default function ImageUploadDemo() {
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);

  const handleImageUpload = (imageUrl: string) => {
    setUploadedImage(imageUrl);
  };

  return (
    <div className="space-y-6">
      <PageBreadcrumb pageTitle="Image Upload Demo" />

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-white/[0.03]">
          <h2 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
            Upload Image to Cloudinary
          </h2>
          <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
            This demo shows how to upload an image to Cloudinary and store the URL in MongoDB.
          </p>

          <ImageUploader
            onImageUpload={handleImageUpload}
            folder="demo"
            className="mt-4"
          />
        </div>

        <div className="rounded-xl border border-gray-200 bg-white p-6 shadow-sm dark:border-gray-800 dark:bg-white/[0.03]">
          <h2 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white">
            Uploaded Image
          </h2>
          
          {uploadedImage ? (
            <div className="space-y-4">
              <div className="relative aspect-w-16 aspect-h-9 overflow-hidden rounded-lg">
                <Image
                  src={uploadedImage}
                  alt="Uploaded image"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="mt-2">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Cloudinary URL:
                </p>
                <p className="mt-1 break-all rounded-md bg-gray-100 p-2 text-xs text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                  {uploadedImage}
                </p>
              </div>
            </div>
          ) : (
            <div className="flex h-64 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-700">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                No image uploaded yet
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
