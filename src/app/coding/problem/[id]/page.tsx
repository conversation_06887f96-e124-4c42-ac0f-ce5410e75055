'use client';

import { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useTheme } from '@/context/ThemeContext';
import MonacoEditor, { editorThemes } from '@/components/coding/MonacoEditor';
import ConfirmationDialog from '@/components/coding/ConfirmationDialog';
import ThemeSelector from '@/components/coding/ThemeSelector';
import LanguageSelector from '@/components/coding/LanguageSelector';
import ExecutionResults from '@/components/coding/ExecutionResults';
import {
  PlayIcon,
  CheckIcon,
  ClockIcon,
  ChevronDownIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowPathIcon,
  ServerIcon,
  PaperAirplaneIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ExclamationCircleIcon,
  XCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
  Cog6ToothIcon,
  ArrowUturnLeftIcon,
  DocumentDuplicateIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';

interface Problem {
  _id: string;
  title: string;
  description: string;
  difficulty: string;
  topics: string[];
  constraints: string[];
  testCases: {
    input: string;
    output: string;
    explanation?: string;
  }[];
  examples?: {
    input: string;
    output: string;
    explanation?: string;
  }[];
}

interface CustomTestCase {
  input: string;
  output: string;
  explanation?: string;
}

interface Submission {
  _id: string;
  userId: string;
  problemId: string;
  code: string;
  language: string;
  results: {
    status: string;
    testResults?: Array<{
      input: string;
      expectedOutput: string;
      actualOutput: string;
      status: string;
      statusCode: number;
      error?: string;
      executionTime?: number;
      memory?: number;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export default function ProblemDetail() {
  const params = useParams();
  const id = params?.id as string | undefined;
  const router = useRouter();
  const { theme } = useTheme();
  
  const [problem, setProblem] = useState<Problem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-language') || 'JavaScript';
    }
    return 'JavaScript';
  });

  const [editorTheme, setEditorTheme] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-theme') || (theme === 'dark' ? 'mockly-dark' : 'vs-light');
    }
    return 'mockly-dark';
  });

  const [submitting, setSubmitting] = useState(false);
  const [activeTestCase, setActiveTestCase] = useState(0);
  const [codeOptionsOpen, setCodeOptionsOpen] = useState(false);
  const [lastSavedCode, setLastSavedCode] = useState<string>('');
  const editorRef = useRef<any>(null);
  const initialLoadRef = useRef<boolean>(true);
  const [runningCode, setRunningCode] = useState(false);
  const [executionResults, setExecutionResults] = useState<any>(null);
  const [submissionResults, setSubmissionResults] = useState<any>(null);

  // Custom test cases
  const [customTestCases, setCustomTestCases] = useState<CustomTestCase[]>([]);
  const [showCustomTestCases, setShowCustomTestCases] = useState(false);
  const [activeCustomTestCase, setActiveCustomTestCase] = useState(0);
  const [usingCustomTestCase, setUsingCustomTestCase] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState<'description' | 'editorial' | 'solutions' | 'submissions'>('description');
  const [testCaseTab, setTestCaseTab] = useState<'examples' | 'results'>('examples');
  const [showTestCaseDetails, setShowTestCaseDetails] = useState(true);
  const [leftPanelWidth, setLeftPanelWidth] = useState(40); // percentage
  const [isResizing, setIsResizing] = useState(false);
  const [testCasePanelCollapsed, setTestCasePanelCollapsed] = useState(false);

  // Submissions state
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loadingSubmissions, setLoadingSubmissions] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);

  // Dialog states
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [retrieveDialogOpen, setRetrieveDialogOpen] = useState(false);
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [infoDialogMessage, setInfoDialogMessage] = useState('');

  // Default starter code templates based on language
  const getStarterCode = (lang: string) => {
    switch (lang) {
      case 'JavaScript':
        return `// Write your complete JavaScript program here
// Read input from stdin and write output to stdout

const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

rl.on('line', (input) => {
    // Parse the input
    const data = input.trim();
    
    // Your solution logic here
    // Example: for addition problem
    // const numbers = data.split(' ').map(Number);
    // const result = numbers[0] + numbers[1];
    
    // Write your solution here
    const result = "Your output here";
    
    console.log(result);
    rl.close();
});`;

      case 'Python':
        return `# Write your complete Python program here
# Read input from stdin and write output to stdout

import sys

def main():
    # Read input
    input_line = input().strip()
    
    # Parse the input
    # Example: for addition problem
    # numbers = list(map(int, input_line.split()))
    # result = numbers[0] + numbers[1]
    
    # Your solution logic here
    result = "Your output here"
    
    # Print the result
    print(result)

if __name__ == "__main__":
    main()`;

      case 'Java':
        return `// Write your complete Java program here
// Read input from stdin and write output to stdout

import java.util.*;
import java.io.*;

public class Solution {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // Read input
        String inputLine = scanner.nextLine().trim();
        
        // Parse the input
        // Example: for addition problem
        // String[] parts = inputLine.split(" ");
        // int a = Integer.parseInt(parts[0]);
        // int b = Integer.parseInt(parts[1]);
        // int result = a + b;
        
        // Your solution logic here
        String result = "Your output here";
        
        // Print the result
        System.out.println(result);
        
        scanner.close();
    }
}`;

      case 'C++':
        return `// Write your complete C++ program here
// Read input from stdin and write output to stdout

#include <iostream>
#include <string>
#include <vector>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iomanip>

using namespace std;

int main() {
    string inputLine;
    getline(cin, inputLine);
    
    // Example implementation for Power(x, n) problem
    // Parse input like "x = 2.00000, n = 10"
    size_t xPos = inputLine.find("x = ");
    size_t nPos = inputLine.find(", n = ");
    
    if (xPos != string::npos && nPos != string::npos) {
        // Extract x and n values
        string xStr = inputLine.substr(xPos + 4, nPos - xPos - 4);
        string nStr = inputLine.substr(nPos + 6);
        
        double x = stod(xStr);
        int n = stoi(nStr);
        
        // Implement power function
        double result = 1.0;
        int absN = abs(n);
        
        for (int i = 0; i < absN; i++) {
            result *= x;
        }
        
        if (n < 0) {
            result = 1.0 / result;
        }
        
        cout << fixed << setprecision(5) << result << endl;
    } else {
        // For other input formats, implement your solution here
        cout << "Your output here" << endl;
    }
    
    return 0;
}`;

      default:
        return '// Write your solution here';
    }
  };

  // Sync editor theme with app theme
  useEffect(() => {
    const newTheme = theme === 'dark' ? 'mockly-dark' : 'vs-light';
    setEditorTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-theme', newTheme);
    }
  }, [theme]);

  // Handle language changes
  useEffect(() => {
    if (initialLoadRef.current) {
      const hasLoadedFromStorage = loadCodeFromLocalStorage();
      if (!hasLoadedFromStorage) {
        setCode(getStarterCode(language));
      }
      initialLoadRef.current = false;
    }
  }, [id]);

  const handleLanguageChange = (newLanguage: string) => {
    if (code.trim() && id) {
      try {
        localStorage.setItem(`code-${id}-${language}`, code);
        setLastSavedCode(code);
      } catch (error) {
        console.error('Error saving code before language change:', error);
      }
    }

    setLanguage(newLanguage);

    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-language', newLanguage);
    }

    setTimeout(() => {
      const savedCode = localStorage.getItem(`code-${id}-${newLanguage}`);
      if (savedCode) {
        setCode(savedCode);
        setLastSavedCode(savedCode);
      } else {
        setCode(getStarterCode(newLanguage));
      }
    }, 0);
  };

  const handleThemeChange = (newTheme: string) => {
    setEditorTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-theme', newTheme);
    }
  };

  useEffect(() => {
    const fetchProblem = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/problems/${id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Problem not found');
          } else {
            const errorText = await response.text();
            throw new Error(`Error: ${response.status}`);
          }
        }

        const data = await response.json();
        setProblem(data);

        const hasLoadedFromStorage = loadCodeFromLocalStorage();
        if (!hasLoadedFromStorage) {
          setCode(getStarterCode(language));
        }
      } catch (error) {
        console.error('Error fetching problem:', error);
        setError(error instanceof Error ? error.message : 'Failed to load problem');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProblem();
    }
  }, [id]);

  // Fetch submissions when submissions tab is active
  useEffect(() => {
    if (activeTab === 'submissions') {
      fetchSubmissions();
    }
  }, [activeTab, id]);

  const handleRunCode = async () => {
    if (!code.trim()) {
      setExecutionResults({
        status: 'Compilation Error',
        statusCode: 6,
        error: 'Please write some code before running'
      });
      return;
    }

    setRunningCode(true);
    setExecutionResults(null);

    try {
      let testCaseToUse;
      
      if (usingCustomTestCase && customTestCases.length > 0) {
        testCaseToUse = customTestCases[activeCustomTestCase];
      } else if (problem?.testCases && problem.testCases.length > 0) {
        testCaseToUse = problem.testCases[activeTestCase];
      } else {
        throw new Error('No test case available');
      }

      // Run against all test cases (LeetCode style)
      const response = await fetch('/api/submissions/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          language,
          problemId: id, // This will run against all test cases
        }),
      });

      if (!response.ok) {
        throw new Error(`Execution failed: ${response.status}`);
      }

      const result = await response.json();
      setExecutionResults(result.results);
    } catch (error) {
      console.error('Error running code:', error);
      setExecutionResults({
        status: 'Internal Error',
        statusCode: 8,
        error: error instanceof Error ? error.message : 'An error occurred while running your code'
      });
    } finally {
      setRunningCode(false);
    }
  };

  const handleSubmit = async () => {
    if (!code.trim()) {
      setSubmissionResults({
        status: 'Compilation Error',
        statusCode: 6,
        error: 'Please write some code before submitting'
      });
      return;
    }

    setSubmitting(true);
    setSubmissionResults(null);

    try {
      saveCodeToLocalStorage(code);

      const response = await fetch('/api/submissions/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          problemId: id,
          code,
          language,
        }),
      });

      if (!response.ok) {
        throw new Error(`Submission failed: ${response.status}`);
      }

      const result = await response.json();
      setSubmissionResults(result.results);
      
      // Refresh submissions if we're on the submissions tab
      if (activeTab === 'submissions') {
        fetchSubmissions();
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      setSubmissionResults({
        status: 'Internal Error',
        statusCode: 8,
        error: error instanceof Error ? error.message : 'An error occurred while submitting your solution'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Custom test case functions
  const addCustomTestCase = () => {
    setCustomTestCases([...customTestCases, { input: '', output: '', explanation: '' }]);
    setActiveCustomTestCase(customTestCases.length);
  };

  const removeCustomTestCase = (index: number) => {
    const newTestCases = customTestCases.filter((_, i) => i !== index);
    setCustomTestCases(newTestCases);
    if (activeCustomTestCase >= newTestCases.length) {
      setActiveCustomTestCase(Math.max(0, newTestCases.length - 1));
    }
  };

  const updateCustomTestCase = (index: number, field: keyof CustomTestCase, value: string) => {
    const newTestCases = [...customTestCases];
    newTestCases[index] = { ...newTestCases[index], [field]: value };
    setCustomTestCases(newTestCases);
  };

  // Fetch submissions for this problem
  const fetchSubmissions = async () => {
    if (!id) return;
    
    setLoadingSubmissions(true);
    try {
      const response = await fetch(`/api/submissions/problem/${id}`);
      if (response.ok) {
        const data = await response.json();
        setSubmissions(data);
      } else {
        console.error('Failed to fetch submissions');
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
    } finally {
      setLoadingSubmissions(false);
    }
  };

  // Save/Load functions
  const saveCodeToLocalStorage = (codeToSave: string) => {
    if (id) {
      try {
        localStorage.setItem(`code-${id}-${language}`, codeToSave);
        setLastSavedCode(codeToSave);
      } catch (error) {
        console.error('Error saving code to localStorage:', error);
      }
    }
  };

  const loadCodeFromLocalStorage = () => {
    if (id) {
      try {
        const savedCode = localStorage.getItem(`code-${id}-${language}`);
        if (savedCode) {
          setCode(savedCode);
          setLastSavedCode(savedCode);
          return true;
        }
      } catch (error) {
        console.error('Error loading code from localStorage:', error);
      }
    }
    return false;
  };

  // Code action functions
  const handleResetCode = () => {
    setResetDialogOpen(true);
    setCodeOptionsOpen(false);
  };

  const confirmResetCode = () => {
    setCode(getStarterCode(language));
  };

  const handleFormatCode = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run();
      setCodeOptionsOpen(false);
    }
  };

  const handleRetrieveLastCode = () => {
    if (lastSavedCode) {
      setRetrieveDialogOpen(true);
      setCodeOptionsOpen(false);
    } else {
      setInfoDialogMessage('No previously saved code found');
      setInfoDialogOpen(true);
      setCodeOptionsOpen(false);
    }
  };

  const confirmRetrieveLastCode = () => {
    setCode(lastSavedCode);
  };

  // Handle panel resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const containerWidth = window.innerWidth;
      const newWidth = (e.clientX / containerWidth) * 100;
      
      // Constrain between 25% and 60%
      if (newWidth >= 25 && newWidth <= 60) {
        setLeftPanelWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+R to reload page
      if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        window.location.reload();
        return;
      }

      // Ctrl+Enter to run code
      if (event.ctrlKey && event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (!runningCode) {
          handleRunCode();
        }
        return;
      }

      // Ctrl+Shift+Enter to submit code
      if (event.ctrlKey && event.shiftKey && event.key === 'Enter') {
        event.preventDefault();
        if (!submitting) {
          handleSubmit();
        }
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [runningCode, submitting]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50 dark:bg-gray-950">
        <div className="h-10 w-10 animate-spin rounded-full border-4 border-gray-300 dark:border-gray-700 border-t-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50 dark:bg-gray-950 text-center px-4">
        <div className="rounded-lg bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-8 max-w-md shadow-lg">
          <XMarkIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-4">Error</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => router.push('/coding/practice')}
            className="px-5 py-2.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors"
          >
            Go to Practice Problems
          </button>
        </div>
      </div>
    );
  }

  if (!problem) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50 dark:bg-gray-950 text-center px-4">
        <div className="rounded-lg bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-8 max-w-md shadow-lg">
          <XMarkIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-yellow-600 dark:text-yellow-400 mb-4">Problem Not Found</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-6">
            The problem you're looking for doesn't exist or has been removed.
          </p>
          <button
            onClick={() => router.push('/coding/practice')}
            className="px-5 py-2.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors"
          >
            Go to Practice Problems
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen leetcode-bg-primary overflow-hidden">
      {/* Left Panel - Problem Description */}
      <div className="w-1/2 flex flex-col leetcode-bg-secondary border-r leetcode-border">
        {/* LeetCode-Style Navigation Tabs */}
        <div className="flex items-center px-4 py-2 border-b leetcode-border">
          <button
            onClick={() => setActiveTab('description')}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded transition-colors mr-2 ${
              activeTab === 'description'
                ? 'leetcode-text-primary bg-gray-700'
                : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
            }`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Description
          </button>
          <button
            onClick={() => setActiveTab('editorial')}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded transition-colors mr-2 ${
              activeTab === 'editorial'
                ? 'leetcode-text-primary bg-gray-700'
                : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
            }`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
            Editorial
          </button>
          <button
            onClick={() => setActiveTab('solutions')}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded transition-colors mr-2 ${
              activeTab === 'solutions'
                ? 'leetcode-text-primary bg-gray-700'
                : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
            }`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            Solutions
          </button>
          <button
            onClick={() => setActiveTab('submissions')}
            className={`flex items-center px-3 py-2 text-sm font-medium rounded transition-colors ${
              activeTab === 'submissions'
                ? 'leetcode-text-primary bg-gray-700'
                : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
            }`}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
            </svg>
            Submissions
          </button>
        </div>

        {/* Problem Header with Title and Difficulty */}
        <div className="px-4 py-4 border-b leetcode-border">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => router.push('/coding/practice')}
                className="flex items-center space-x-2 text-sm leetcode-text-secondary hover:leetcode-text-primary transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <h1 className="text-xl font-semibold leetcode-text-primary">
                {problem.title}
              </h1>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm leetcode-text-muted">Solved</span>
              <CheckCircleIcon className="h-5 w-5 text-green-500" />
            </div>
          </div>

          {/* Difficulty and Topics */}
          <div className="flex items-center space-x-4 mb-3">
            <span className={`px-2 py-1 text-sm font-medium rounded ${
              problem.difficulty === 'Easy' ? 'leetcode-easy bg-green-900/20' :
              problem.difficulty === 'Medium' ? 'leetcode-medium bg-yellow-900/20' :
              'leetcode-hard bg-red-900/20'
            }`}>
              {problem.difficulty}
            </span>
            <button className="flex items-center space-x-1 text-sm leetcode-text-muted hover:leetcode-text-primary">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              <span>Topics</span>
            </button>
            <button className="flex items-center space-x-1 text-sm leetcode-text-muted hover:leetcode-text-primary">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              <span>Companies</span>
            </button>
          </div>
        </div>

        {/* Problem Content */}
        <div className="flex-1 overflow-y-auto leetcode-scrollbar">
          {activeTab === 'description' && (
            <div className="p-4">
              {/* Problem Description */}
              <div className="prose prose-sm prose-invert max-w-none mb-6">
                <div
                  className="leetcode-text-secondary leading-relaxed"
                  dangerouslySetInnerHTML={{ __html: problem.description }}
                />
              </div>

              {/* Examples */}
              {problem.testCases && problem.testCases.length > 0 && (
                <div className="mb-6">
                  {problem.testCases.slice(0, 2).map((testCase, index) => (
                    <div key={index} className="mb-4">
                      <h3 className="text-sm font-semibold leetcode-text-primary mb-2">
                        Example {index + 1}:
                      </h3>
                      <div className="leetcode-bg-tertiary rounded p-3 border leetcode-border">
                        <div className="mb-2">
                          <span className="text-sm font-medium leetcode-text-primary">Input: </span>
                          <span className="text-sm font-mono leetcode-text-secondary">{testCase.input}</span>
                        </div>
                        <div className="mb-2">
                          <span className="text-sm font-medium leetcode-text-primary">Output: </span>
                          <span className="text-sm font-mono leetcode-text-secondary">{testCase.output}</span>
                        </div>
                        {testCase.explanation && (
                          <div>
                            <span className="text-sm font-medium leetcode-text-primary">Explanation: </span>
                            <span className="text-sm leetcode-text-secondary">{testCase.explanation}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Constraints */}
              {problem.constraints && problem.constraints.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-sm font-semibold leetcode-text-primary mb-2">Constraints:</h3>
                  <ul className="space-y-1">
                    {problem.constraints.map((constraint, index) => (
                      <li key={index} className="text-sm leetcode-text-secondary font-mono">
                        • {constraint}
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Acceptance Rate */}
              <div className="mb-6 p-4 leetcode-bg-tertiary rounded border leetcode-border">
                <div className="flex items-center justify-between text-sm">
                  <span className="leetcode-text-muted">Seen this question in a real interview before?</span>
                  <span className="leetcode-text-primary">1/5</span>
                </div>
                <div className="flex space-x-4 mt-2">
                  <button className="px-3 py-1 text-xs bg-gray-700 leetcode-text-muted rounded hover:bg-gray-600">
                    Yes
                  </button>
                  <button className="px-3 py-1 text-xs bg-gray-700 leetcode-text-muted rounded hover:bg-gray-600">
                    No
                  </button>
                </div>
                <div className="mt-4 pt-4 border-t leetcode-border">
                  <div className="flex justify-between text-sm">
                    <div>
                      <span className="leetcode-text-muted">Accepted </span>
                      <span className="leetcode-text-primary font-medium">592,457</span>
                      <span className="leetcode-text-muted text-xs"> / 1.1M</span>
                    </div>
                    <div>
                      <span className="leetcode-text-muted">Acceptance Rate </span>
                      <span className="leetcode-text-primary font-medium">53.7%</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Topics Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold leetcode-text-primary flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    Topics
                  </h3>
                  <button className="text-xs leetcode-text-muted hover:leetcode-text-primary">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </button>
                </div>
                {problem.topics && problem.topics.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {problem.topics.map((topic, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-xs font-medium leetcode-text-muted bg-gray-700 rounded-full hover:bg-gray-600 cursor-pointer transition-colors"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Similar Questions Section */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold leetcode-text-primary flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    Similar Questions
                  </h3>
                  <button className="text-xs leetcode-text-muted hover:leetcode-text-primary">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </button>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-2 leetcode-bg-tertiary rounded border leetcode-border hover:bg-gray-700 cursor-pointer transition-colors">
                    <span className="text-sm leetcode-text-primary">Add Two Numbers</span>
                    <span className="text-xs leetcode-medium">Medium</span>
                  </div>
                  <div className="flex items-center justify-between p-2 leetcode-bg-tertiary rounded border leetcode-border hover:bg-gray-700 cursor-pointer transition-colors">
                    <span className="text-sm leetcode-text-primary">Three Sum</span>
                    <span className="text-xs leetcode-medium">Medium</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'editorial' && (
            <div className="p-4">
              <div className="text-center py-12">
                <svg className="w-12 h-12 mx-auto mb-4 leetcode-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">Editorial</h3>
                <p className="leetcode-text-muted">Editorial content will be available soon.</p>
              </div>
            </div>
          )}

          {activeTab === 'solutions' && (
            <div className="p-4">
              <div className="text-center py-12">
                <svg className="w-12 h-12 mx-auto mb-4 leetcode-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">Solutions</h3>
                <p className="leetcode-text-muted">Community solutions will be available soon.</p>
              </div>
            </div>
          )}

          {activeTab === 'submissions' && (
            <div className="p-4">
              <div className="text-center py-12">
                <svg className="w-12 h-12 mx-auto mb-4 leetcode-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">Submissions</h3>
                <p className="leetcode-text-muted">Your submission history will appear here.</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Right Panel - Code Editor */}
      <div className="w-1/2 flex flex-col leetcode-bg-primary">
        {/* Editor Header */}
        <div className="flex items-center justify-between px-4 py-2 leetcode-bg-secondary border-b leetcode-border">
          <div className="flex items-center space-x-3">
            <span className="text-sm font-medium leetcode-text-primary">Code</span>
            <LanguageSelector
              selectedLanguage={language}
              onLanguageChange={handleLanguageChange}
              languages={['JavaScript', 'Python', 'Java', 'C++']}
            />
            <span className="text-xs leetcode-text-muted">Auto</span>
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-1.5 leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
            </button>
            <button className="p-1.5 leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
            <button className="p-1.5 leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        {/* Code Editor */}
        <div className="flex-1 overflow-hidden">
          <MonacoEditor
            language={language}
            value={code}
            onChange={(value) => {
              setCode(value || '');
              if (value) saveCodeToLocalStorage(value);
            }}
            theme={editorTheme}
            height="100%"
          />
        </div>

        {/* Bottom Panel - Test Results */}
        <div className="border-t leetcode-border leetcode-bg-secondary" style={{ height: testCasePanelCollapsed ? '48px' : '300px' }}>
          {/* Test Case Header */}
          <div className="flex items-center justify-between px-4 py-2 border-b leetcode-border">
            <div className="flex items-center space-x-1">
              <button
                onClick={() => {
                  setTestCaseTab('examples');
                  setUsingCustomTestCase(false);
                }}
                className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
                  testCaseTab === 'examples'
                    ? 'leetcode-text-primary bg-gray-700'
                    : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
                }`}
              >
                Testcase
              </button>
              <button
                onClick={() => setTestCaseTab('results')}
                className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
                  testCaseTab === 'results'
                    ? 'leetcode-text-primary bg-gray-700'
                    : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
                }`}
              >
                Test Result
              </button>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleRunCode}
                disabled={runningCode}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 rounded transition-colors"
              >
                {runningCode ? (
                  <ArrowPathIcon className="h-4 w-4 mr-1.5 animate-spin" />
                ) : (
                  <PlayIcon className="h-4 w-4 mr-1.5" />
                )}
                Run
              </button>
              <button
                onClick={handleSubmit}
                disabled={submitting}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-500 rounded transition-colors"
              >
                {submitting ? (
                  <ArrowPathIcon className="h-4 w-4 mr-1.5 animate-spin" />
                ) : (
                  <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                )}
                Submit
              </button>
            </div>
          </div>

          {/* Test Case Content */}
          {!testCasePanelCollapsed && (
            <div className="flex-1 flex overflow-hidden">
              {testCaseTab === 'examples' && (
                <div className="flex-1 p-3">
                  {problem.testCases && problem.testCases.length > 0 && (
                    <div className="space-y-3">
                      {/* Test Case Selector */}
                      <div className="flex space-x-1 flex-wrap">
                        {problem.testCases.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setActiveTestCase(index)}
                            className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
                              activeTestCase === index
                                ? 'leetcode-text-primary bg-gray-700'
                                : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
                            }`}
                          >
                            Case {index + 1}
                          </button>
                        ))}
                      </div>

                      {/* Test Case Input/Output */}
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">a =</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary">
                              {problem.testCases[activeTestCase]?.input.split(' ')[0] || '1'}
                            </pre>
                          </div>
                        </div>
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">b =</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary">
                              {problem.testCases[activeTestCase]?.input.split(' ')[1] || '2'}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {testCaseTab === 'results' && (
                <div className="flex-1 p-3">
                  {executionResults ? (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                        <span className="text-sm font-medium text-green-500">Accepted</span>
                        <span className="text-xs leetcode-text-muted">Runtime: 0 ms</span>
                      </div>

                      <div className="flex space-x-1">
                        <button className="px-3 py-1.5 text-sm font-medium rounded bg-gray-700 leetcode-text-primary">
                          Case 1
                        </button>
                        <button className="px-3 py-1.5 text-sm font-medium rounded leetcode-text-muted hover:bg-gray-700">
                          Case 2
                        </button>
                      </div>

                      <div className="space-y-3">
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">Input</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary">a = 1, b = 2</pre>
                          </div>
                        </div>
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">Output</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary">3</pre>
                          </div>
                        </div>
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">Expected</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary">3</pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="leetcode-text-muted mb-2">
                        <svg className="h-8 w-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <p className="text-sm leetcode-text-muted">You must run your code first</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Dialogs */}
      <ConfirmationDialog
        isOpen={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        onConfirm={confirmResetCode}
        title="Reset Code"
        message="Are you sure you want to reset your code? This will revert to the starter template."
        confirmText="Reset"
        variant="warning"
      />

      <ConfirmationDialog
        isOpen={retrieveDialogOpen}
        onClose={() => setRetrieveDialogOpen(false)}
        onConfirm={confirmRetrieveLastCode}
        title="Retrieve Last Code"
        message="Are you sure you want to restore your last saved code?"
        confirmText="Restore"
        variant="info"
      />

      <ConfirmationDialog
        isOpen={infoDialogOpen}
        onClose={() => setInfoDialogOpen(false)}
        onConfirm={() => setInfoDialogOpen(false)}
        title="Information"
        message={infoDialogMessage}
        confirmText="OK"
        cancelText=""
        variant="info"
      />
    </div>
  );
}