'use client';

import { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useTheme } from '@/context/ThemeContext';
import MonacoEditor, { editorThemes } from '@/components/coding/MonacoEditor';
import ConfirmationDialog from '@/components/coding/ConfirmationDialog';
import ThemeSelector from '@/components/coding/ThemeSelector';
import LanguageSelector from '@/components/coding/LanguageSelector';
import ExecutionResults from '@/components/coding/ExecutionResults';
import {
  PlayIcon,
  CheckIcon,
  ClockIcon,
  ChevronDownIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowPathIcon,
  ServerIcon,
  PaperAirplaneIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ExclamationCircleIcon,
  XCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
  Cog6ToothIcon,
  ArrowUturnLeftIcon,
  DocumentDuplicateIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';

interface Problem {
  _id: string;
  title: string;
  description: string;
  difficulty: string;
  topics: string[];
  constraints: string[];
  testCases: {
    input: string;
    output: string;
    explanation?: string;
  }[];
  examples?: {
    input: string;
    output: string;
    explanation?: string;
  }[];
}

interface CustomTestCase {
  input: string;
  output: string;
  explanation?: string;
}

interface Submission {
  _id: string;
  userId: string;
  problemId: string;
  code: string;
  language: string;
  results: {
    status: string;
    testResults?: Array<{
      input: string;
      expectedOutput: string;
      actualOutput: string;
      status: string;
      statusCode: number;
      error?: string;
      executionTime?: number;
      memory?: number;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export default function ProblemDetail() {
  const params = useParams();
  const id = params?.id as string | undefined;
  const router = useRouter();
  const { theme } = useTheme();
  
  const [problem, setProblem] = useState<Problem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-language') || 'JavaScript';
    }
    return 'JavaScript';
  });

  const [selectedLanguage, setSelectedLanguage] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-language') || 'javascript';
    }
    return 'javascript';
  });

  const [editorTheme, setEditorTheme] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-theme') || (theme === 'dark' ? 'mockly-dark' : 'vs-light');
    }
    return 'mockly-dark';
  });

  const [submitting, setSubmitting] = useState(false);
  const [activeTestCase, setActiveTestCase] = useState(0);
  const [codeOptionsOpen, setCodeOptionsOpen] = useState(false);
  const [lastSavedCode, setLastSavedCode] = useState<string>('');
  const editorRef = useRef<any>(null);
  const initialLoadRef = useRef<boolean>(true);
  const [runningCode, setRunningCode] = useState(false);
  const [executionResults, setExecutionResults] = useState<any>(null);
  const [submissionResults, setSubmissionResults] = useState<any>(null);

  // Custom test cases
  const [customTestCases, setCustomTestCases] = useState<CustomTestCase[]>([]);
  const [showCustomTestCases, setShowCustomTestCases] = useState(false);
  const [activeCustomTestCase, setActiveCustomTestCase] = useState(0);
  const [usingCustomTestCase, setUsingCustomTestCase] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState<'description' | 'editorial' | 'solutions' | 'submissions'>('description');
  const [testCaseTab, setTestCaseTab] = useState<'examples' | 'results'>('examples');
  const [showTestCaseDetails, setShowTestCaseDetails] = useState(true);
  const [leftPanelWidth, setLeftPanelWidth] = useState(40); // percentage
  const [isResizing, setIsResizing] = useState(false);
  const [testCasePanelCollapsed, setTestCasePanelCollapsed] = useState(false);

  // Submissions state
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loadingSubmissions, setLoadingSubmissions] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);

  // Dialog states
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [retrieveDialogOpen, setRetrieveDialogOpen] = useState(false);
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [infoDialogMessage, setInfoDialogMessage] = useState('');

  // Default starter code templates based on language
  const getStarterCode = (lang: string) => {
    switch (lang) {
      case 'JavaScript':
        return `// Write your complete JavaScript program here
// Read input from stdin and write output to stdout

const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

rl.on('line', (input) => {
    // Parse the input
    const data = input.trim();
    
    // Your solution logic here
    // Example: for addition problem
    // const numbers = data.split(' ').map(Number);
    // const result = numbers[0] + numbers[1];
    
    // Write your solution here
    const result = "Your output here";
    
    console.log(result);
    rl.close();
});`;

      case 'Python':
        return `# Write your complete Python program here
# Read input from stdin and write output to stdout

import sys

def main():
    # Read input
    input_line = input().strip()
    
    # Parse the input
    # Example: for addition problem
    # numbers = list(map(int, input_line.split()))
    # result = numbers[0] + numbers[1]
    
    # Your solution logic here
    result = "Your output here"
    
    # Print the result
    print(result)

if __name__ == "__main__":
    main()`;

      case 'Java':
        return `// Write your complete Java program here
// Read input from stdin and write output to stdout

import java.util.*;
import java.io.*;

public class Solution {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // Read input
        String inputLine = scanner.nextLine().trim();
        
        // Parse the input
        // Example: for addition problem
        // String[] parts = inputLine.split(" ");
        // int a = Integer.parseInt(parts[0]);
        // int b = Integer.parseInt(parts[1]);
        // int result = a + b;
        
        // Your solution logic here
        String result = "Your output here";
        
        // Print the result
        System.out.println(result);
        
        scanner.close();
    }
}`;

      case 'C++':
        return `// Write your complete C++ program here
// Read input from stdin and write output to stdout

#include <iostream>
#include <string>
#include <vector>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iomanip>

using namespace std;

int main() {
    string inputLine;
    getline(cin, inputLine);
    
    // Example implementation for Power(x, n) problem
    // Parse input like "x = 2.00000, n = 10"
    size_t xPos = inputLine.find("x = ");
    size_t nPos = inputLine.find(", n = ");
    
    if (xPos != string::npos && nPos != string::npos) {
        // Extract x and n values
        string xStr = inputLine.substr(xPos + 4, nPos - xPos - 4);
        string nStr = inputLine.substr(nPos + 6);
        
        double x = stod(xStr);
        int n = stoi(nStr);
        
        // Implement power function
        double result = 1.0;
        int absN = abs(n);
        
        for (int i = 0; i < absN; i++) {
            result *= x;
        }
        
        if (n < 0) {
            result = 1.0 / result;
        }
        
        cout << fixed << setprecision(5) << result << endl;
    } else {
        // For other input formats, implement your solution here
        cout << "Your output here" << endl;
    }
    
    return 0;
}`;

      default:
        return '// Write your solution here';
    }
  };

  // Sync editor theme with app theme
  useEffect(() => {
    const newTheme = theme === 'dark' ? 'mockly-dark' : 'vs-light';
    setEditorTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-theme', newTheme);
    }
  }, [theme]);

  // Handle language changes
  useEffect(() => {
    if (initialLoadRef.current) {
      const hasLoadedFromStorage = loadCodeFromLocalStorage();
      if (!hasLoadedFromStorage) {
        setCode(getStarterCode(language));
      }
      initialLoadRef.current = false;
    }
  }, [id]);

  const handleLanguageChange = (newLanguage: string) => {
    if (code.trim() && id) {
      try {
        localStorage.setItem(`code-${id}-${language}`, code);
        setLastSavedCode(code);
      } catch (error) {
        console.error('Error saving code before language change:', error);
      }
    }

    setLanguage(newLanguage);

    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-language', newLanguage);
    }

    setTimeout(() => {
      const savedCode = localStorage.getItem(`code-${id}-${newLanguage}`);
      if (savedCode) {
        setCode(savedCode);
        setLastSavedCode(savedCode);
      } else {
        setCode(getStarterCode(newLanguage));
      }
    }, 0);
  };

  const handleThemeChange = (newTheme: string) => {
    setEditorTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-theme', newTheme);
    }
  };

  useEffect(() => {
    const fetchProblem = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/problems/${id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Problem not found');
          } else {
            const errorText = await response.text();
            throw new Error(`Error: ${response.status}`);
          }
        }

        const data = await response.json();
        setProblem(data);

        const hasLoadedFromStorage = loadCodeFromLocalStorage();
        if (!hasLoadedFromStorage) {
          setCode(getStarterCode(language));
        }
      } catch (error) {
        console.error('Error fetching problem:', error);
        setError(error instanceof Error ? error.message : 'Failed to load problem');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProblem();
    }
  }, [id]);

  // Fetch submissions when submissions tab is active
  useEffect(() => {
    if (activeTab === 'submissions') {
      fetchSubmissions();
    }
  }, [activeTab, id]);

  const handleRunCode = async () => {
    if (!code.trim()) {
      setExecutionResults({
        status: 'Compilation Error',
        statusCode: 6,
        error: 'Please write some code before running'
      });
      return;
    }

    setRunningCode(true);
    setExecutionResults(null);

    try {
      let testCaseToUse;
      
      if (usingCustomTestCase && customTestCases.length > 0) {
        testCaseToUse = customTestCases[activeCustomTestCase];
      } else if (problem?.testCases && problem.testCases.length > 0) {
        testCaseToUse = problem.testCases[activeTestCase];
      } else {
        throw new Error('No test case available');
      }

      // Run against all test cases (LeetCode style)
      const response = await fetch('/api/submissions/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          language,
          problemId: id, // This will run against all test cases
        }),
      });

      if (!response.ok) {
        throw new Error(`Execution failed: ${response.status}`);
      }

      const result = await response.json();
      setExecutionResults(result.results);
    } catch (error) {
      console.error('Error running code:', error);
      setExecutionResults({
        status: 'Internal Error',
        statusCode: 8,
        error: error instanceof Error ? error.message : 'An error occurred while running your code'
      });
    } finally {
      setRunningCode(false);
    }
  };

  const handleSubmit = async () => {
    if (!code.trim()) {
      setSubmissionResults({
        status: 'Compilation Error',
        statusCode: 6,
        error: 'Please write some code before submitting'
      });
      return;
    }

    setSubmitting(true);
    setSubmissionResults(null);

    try {
      saveCodeToLocalStorage(code);

      const response = await fetch('/api/submissions/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          problemId: id,
          code,
          language,
        }),
      });

      if (!response.ok) {
        throw new Error(`Submission failed: ${response.status}`);
      }

      const result = await response.json();
      setSubmissionResults(result.results);
      
      // Refresh submissions if we're on the submissions tab
      if (activeTab === 'submissions') {
        fetchSubmissions();
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      setSubmissionResults({
        status: 'Internal Error',
        statusCode: 8,
        error: error instanceof Error ? error.message : 'An error occurred while submitting your solution'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Custom test case functions
  const addCustomTestCase = () => {
    setCustomTestCases([...customTestCases, { input: '', output: '', explanation: '' }]);
    setActiveCustomTestCase(customTestCases.length);
  };

  const removeCustomTestCase = (index: number) => {
    const newTestCases = customTestCases.filter((_, i) => i !== index);
    setCustomTestCases(newTestCases);
    if (activeCustomTestCase >= newTestCases.length) {
      setActiveCustomTestCase(Math.max(0, newTestCases.length - 1));
    }
  };

  const updateCustomTestCase = (index: number, field: keyof CustomTestCase, value: string) => {
    const newTestCases = [...customTestCases];
    newTestCases[index] = { ...newTestCases[index], [field]: value };
    setCustomTestCases(newTestCases);
  };

  // Fetch submissions for this problem
  const fetchSubmissions = async () => {
    if (!id) return;
    
    setLoadingSubmissions(true);
    try {
      const response = await fetch(`/api/submissions/problem/${id}`);
      if (response.ok) {
        const data = await response.json();
        setSubmissions(data);
      } else {
        console.error('Failed to fetch submissions');
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
    } finally {
      setLoadingSubmissions(false);
    }
  };

  // Save/Load functions
  const saveCodeToLocalStorage = (codeToSave: string) => {
    if (id) {
      try {
        localStorage.setItem(`code-${id}-${language}`, codeToSave);
        setLastSavedCode(codeToSave);
      } catch (error) {
        console.error('Error saving code to localStorage:', error);
      }
    }
  };

  const loadCodeFromLocalStorage = () => {
    if (id) {
      try {
        const savedCode = localStorage.getItem(`code-${id}-${language}`);
        if (savedCode) {
          setCode(savedCode);
          setLastSavedCode(savedCode);
          return true;
        }
      } catch (error) {
        console.error('Error loading code from localStorage:', error);
      }
    }
    return false;
  };

  // Code action functions
  const handleResetCode = () => {
    setResetDialogOpen(true);
    setCodeOptionsOpen(false);
  };

  const confirmResetCode = () => {
    setCode(getStarterCode(language));
  };

  const handleFormatCode = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run();
      setCodeOptionsOpen(false);
    }
  };

  const handleRetrieveLastCode = () => {
    if (lastSavedCode) {
      setRetrieveDialogOpen(true);
      setCodeOptionsOpen(false);
    } else {
      setInfoDialogMessage('No previously saved code found');
      setInfoDialogOpen(true);
      setCodeOptionsOpen(false);
    }
  };

  const confirmRetrieveLastCode = () => {
    setCode(lastSavedCode);
  };

  // Handle panel resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const containerWidth = window.innerWidth;
      const newWidth = (e.clientX / containerWidth) * 100;
      
      // Constrain between 25% and 60%
      if (newWidth >= 25 && newWidth <= 60) {
        setLeftPanelWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+R to reload page
      if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        window.location.reload();
        return;
      }

      // Ctrl+Enter to run code
      if (event.ctrlKey && event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (!runningCode) {
          handleRunCode();
        }
        return;
      }

      // Ctrl+Shift+Enter to submit code
      if (event.ctrlKey && event.shiftKey && event.key === 'Enter') {
        event.preventDefault();
        if (!submitting) {
          handleSubmit();
        }
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [runningCode, submitting]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50 dark:bg-gray-950">
        <div className="h-10 w-10 animate-spin rounded-full border-4 border-gray-300 dark:border-gray-700 border-t-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50 dark:bg-gray-950 text-center px-4">
        <div className="rounded-lg bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-8 max-w-md shadow-lg">
          <XMarkIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-4">Error</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => router.push('/coding/practice')}
            className="px-5 py-2.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors"
          >
            Go to Practice Problems
          </button>
        </div>
      </div>
    );
  }

  if (!problem) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50 dark:bg-gray-950 text-center px-4">
        <div className="rounded-lg bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-8 max-w-md shadow-lg">
          <XMarkIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-yellow-600 dark:text-yellow-400 mb-4">Problem Not Found</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-6">
            The problem you're looking for doesn't exist or has been removed.
          </p>
          <button
            onClick={() => router.push('/coding/practice')}
            className="px-5 py-2.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors"
          >
            Go to Practice Problems
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900 overflow-hidden transition-colors">
      {/* Left Panel - Problem Description */}
      <div
        className="flex flex-col bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transition-colors"
        style={{ width: `${leftPanelWidth}%` }}
      >
        {/* Modern Navigation Tabs */}
        <div className="flex items-center px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
          <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <button
              onClick={() => setActiveTab('description')}
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                activeTab === 'description'
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Description
            </button>
            <button
              onClick={() => setActiveTab('editorial')}
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                activeTab === 'editorial'
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
              Editorial
            </button>
            <button
              onClick={() => setActiveTab('solutions')}
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                activeTab === 'solutions'
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
              Solutions
            </button>
            <button
              onClick={() => setActiveTab('submissions')}
              className={`flex items-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                activeTab === 'submissions'
                  ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
              </svg>
              Submissions
            </button>
          </div>
        </div>

        {/* Enhanced Problem Header */}
        <div className="px-6 py-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/coding/practice')}
                className="flex items-center justify-center w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {problem.title}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  Problem #{problem._id?.slice(-6) || '001'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 px-3 py-1.5 bg-green-100 dark:bg-green-900/30 rounded-full">
                <CheckCircleIcon className="h-4 w-4 text-green-600 dark:text-green-400" />
                <span className="text-sm font-medium text-green-700 dark:text-green-300">Solved</span>
              </div>
            </div>
          </div>

          {/* Enhanced Difficulty and Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium ${
                problem.difficulty === 'Easy'
                  ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' :
                problem.difficulty === 'Medium'
                  ? 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300' :
                  'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300'
              }`}>
                {problem.difficulty}
              </span>
              <div className="flex items-center space-x-2">
                <button className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                  Topics
                </button>
                <button className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Companies
                </button>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </button>
              <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Enhanced Problem Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'description' && (
            <div className="p-6">
              {/* Problem Description */}
              <div className="prose prose-sm max-w-none mb-8">
                <div
                  className="text-gray-700 dark:text-gray-300 leading-relaxed text-base"
                  dangerouslySetInnerHTML={{ __html: problem.description }}
                />
              </div>

              {/* Enhanced Examples */}
              {problem.testCases && problem.testCases.length > 0 && (
                <div className="mb-8">
                  {problem.testCases.slice(0, 2).map((testCase, index) => (
                    <div key={index} className="mb-6">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center">
                        <span className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                          {index + 1}
                        </span>
                        Example {index + 1}:
                      </h3>
                      <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                        <div className="space-y-3">
                          <div className="flex flex-col sm:flex-row sm:items-center">
                            <span className="text-sm font-semibold text-gray-900 dark:text-white min-w-[80px]">Input:</span>
                            <code className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded text-gray-800 dark:text-gray-200 mt-1 sm:mt-0">
                              {testCase.input}
                            </code>
                          </div>
                          <div className="flex flex-col sm:flex-row sm:items-center">
                            <span className="text-sm font-semibold text-gray-900 dark:text-white min-w-[80px]">Output:</span>
                            <code className="text-sm font-mono bg-gray-100 dark:bg-gray-800 px-3 py-1 rounded text-gray-800 dark:text-gray-200 mt-1 sm:mt-0">
                              {testCase.output}
                            </code>
                          </div>
                          {testCase.explanation && (
                            <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                              <span className="text-sm font-semibold text-gray-900 dark:text-white">Explanation:</span>
                              <p className="text-sm text-gray-700 dark:text-gray-300 mt-1">{testCase.explanation}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Enhanced Constraints */}
              {problem.constraints && problem.constraints.length > 0 && (
                <div className="mb-8">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
                    <svg className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Constraints:
                  </h3>
                  <div className="bg-gray-50 dark:bg-gray-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                    <ul className="space-y-2">
                      {problem.constraints.map((constraint, index) => (
                        <li key={index} className="flex items-start">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                          <code className="text-sm font-mono text-gray-700 dark:text-gray-300">{constraint}</code>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              )}

              {/* Enhanced Acceptance Rate */}
              <div className="mb-8 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl p-6 border border-blue-200 dark:border-blue-800">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Interview Experience</h3>
                  <div className="flex items-center space-x-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <svg
                        key={star}
                        className={`w-4 h-4 ${star <= 1 ? 'text-yellow-400' : 'text-gray-300 dark:text-gray-600'}`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-sm text-gray-700 dark:text-gray-300 mb-4">
                  Seen this question in a real interview before?
                </p>
                <div className="flex space-x-3 mb-6">
                  <button className="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-lg transition-colors">
                    Yes
                  </button>
                  <button className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded-lg transition-colors">
                    No
                  </button>
                </div>
                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-blue-200 dark:border-blue-700">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 dark:text-white">592,457</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Accepted</div>
                    <div className="text-xs text-gray-500 dark:text-gray-500">out of 1.1M</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">53.7%</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">Acceptance Rate</div>
                  </div>
                </div>
              </div>

              {/* Enhanced Topics Section */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <svg className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                    </svg>
                    Related Topics
                  </h3>
                  <button className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </button>
                </div>
                {problem.topics && problem.topics.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {problem.topics.map((topic, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-blue-900/30 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/50 cursor-pointer transition-colors"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Enhanced Similar Questions */}
              <div className="mb-8">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <svg className="w-5 h-5 mr-2 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Similar Questions
                  </h3>
                  <button className="text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900/50 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800/50 cursor-pointer transition-colors group">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        Add Two Numbers
                      </span>
                    </div>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                      Medium
                    </span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-900/50 rounded-xl border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800/50 cursor-pointer transition-colors group">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                        Three Sum
                      </span>
                    </div>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                      Medium
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'editorial' && (
            <div className="p-4">
              <div className="text-center py-12">
                <svg className="w-12 h-12 mx-auto mb-4 leetcode-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">Editorial</h3>
                <p className="leetcode-text-muted">Editorial content will be available soon.</p>
              </div>
            </div>
          )}

          {activeTab === 'solutions' && (
            <div className="p-4">
              <div className="text-center py-12">
                <svg className="w-12 h-12 mx-auto mb-4 leetcode-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">Solutions</h3>
                <p className="leetcode-text-muted">Community solutions will be available soon.</p>
              </div>
            </div>
          )}

          {activeTab === 'submissions' && (
            <div className="p-4">
              <div className="text-center py-12">
                <svg className="w-12 h-12 mx-auto mb-4 leetcode-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                </svg>
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">Submissions</h3>
                <p className="leetcode-text-muted">Your submission history will appear here.</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Resizer */}
      <div
        className="w-1 bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 dark:hover:bg-blue-400 cursor-col-resize transition-colors"
        onMouseDown={(e) => {
          e.preventDefault();
          setIsResizing(true);
        }}
      />

      {/* Enhanced Right Panel - Code Editor */}
      <div
        className="flex flex-col bg-white dark:bg-gray-800 transition-colors"
        style={{ width: `${100 - leftPanelWidth}%` }}
      >
        {/* Enhanced Editor Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Code</span>
              <LanguageSelector
                selectedLanguage={language}
                onLanguageChange={handleLanguageChange}
                languages={['JavaScript', 'Python', 'Java', 'C++']}
              />
            </div>
            <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 dark:bg-green-900/30 rounded-full">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-xs font-medium text-green-700 dark:text-green-300">Auto-save</span>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
            </button>
            <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
            <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        {/* Code Editor */}
        <div className="flex-1 overflow-hidden">
          <MonacoEditor
            language={language}
            value={code}
            onChange={(value) => {
              setCode(value || '');
              if (value) saveCodeToLocalStorage(value);
            }}
            theme={editorTheme}
            height="100%"
          />
        </div>

        {/* Enhanced Bottom Panel - Test Results */}
        <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50" style={{ height: testCasePanelCollapsed ? '48px' : '300px' }}>
          {/* Enhanced Test Case Header */}
          <div className="flex items-center justify-between px-6 py-3 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
              <button
                onClick={() => {
                  setTestCaseTab('examples');
                  setUsingCustomTestCase(false);
                }}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${
                  testCaseTab === 'examples'
                    ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }`}
              >
                Testcase
              </button>
              <button
                onClick={() => setTestCaseTab('results')}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-all duration-200 ${
                  testCaseTab === 'results'
                    ? 'bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-400 shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700/50'
                }`}
              >
                Test Result
              </button>
            </div>

            <div className="flex items-center space-x-3">
              <button
                onClick={handleRunCode}
                disabled={runningCode}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 rounded-lg transition-colors shadow-sm"
              >
                {runningCode ? (
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <PlayIcon className="h-4 w-4 mr-2" />
                )}
                Run
              </button>
              <button
                onClick={handleSubmit}
                disabled={submitting}
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-400 rounded-lg transition-colors shadow-sm"
              >
                {submitting ? (
                  <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                )}
                Submit
              </button>
            </div>
          </div>

          {/* Enhanced Test Case Content */}
          {!testCasePanelCollapsed && (
            <div className="flex-1 flex overflow-hidden bg-white dark:bg-gray-800">
              {testCaseTab === 'examples' && (
                <div className="flex-1 p-6">
                  {problem.testCases && problem.testCases.length > 0 && (
                    <div className="space-y-4">
                      {/* Enhanced Test Case Selector */}
                      <div className="flex space-x-2 flex-wrap">
                        {problem.testCases.map((_, index) => (
                          <button
                            key={index}
                            onClick={() => setActiveTestCase(index)}
                            className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                              activeTestCase === index
                                ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-300 dark:border-blue-600'
                                : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600'
                            }`}
                          >
                            Case {index + 1}
                          </button>
                        ))}
                      </div>

                      {/* Enhanced Test Case Input/Output */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Input: a =
                          </label>
                          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <pre className="text-sm font-mono text-gray-900 dark:text-gray-100">
                              {problem.testCases[activeTestCase]?.input.split(' ')[0] || '1'}
                            </pre>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Input: b =
                          </label>
                          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <pre className="text-sm font-mono text-gray-900 dark:text-gray-100">
                              {problem.testCases[activeTestCase]?.input.split(' ')[1] || '2'}
                            </pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {testCaseTab === 'results' && (
                <div className="flex-1 p-6">
                  {executionResults ? (
                    <div className="space-y-6">
                      <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <div className="flex items-center space-x-3">
                          <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
                          <div>
                            <span className="text-lg font-semibold text-green-700 dark:text-green-300">Accepted</span>
                            <div className="flex items-center space-x-4 mt-1 text-sm text-green-600 dark:text-green-400">
                              <span>Runtime: 0 ms</span>
                              <span>Memory: 42.1 MB</span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm text-green-600 dark:text-green-400">Beats 95.2%</div>
                          <div className="text-xs text-green-500 dark:text-green-500">of submissions</div>
                        </div>
                      </div>

                      <div className="flex space-x-2">
                        <button className="px-4 py-2 text-sm font-medium rounded-lg bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 border border-blue-300 dark:border-blue-600">
                          Case 1
                        </button>
                        <button className="px-4 py-2 text-sm font-medium rounded-lg bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 border border-gray-300 dark:border-gray-600">
                          Case 2
                        </button>
                      </div>

                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Input</label>
                          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <pre className="text-sm font-mono text-gray-900 dark:text-gray-100">a = 1, b = 2</pre>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Output</label>
                          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <pre className="text-sm font-mono text-gray-900 dark:text-gray-100">3</pre>
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Expected</label>
                          <div className="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                            <pre className="text-sm font-mono text-gray-900 dark:text-gray-100">3</pre>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                        <svg className="h-8 w-8 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No Results Yet</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Run your code to see test results here</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Confirmation Dialogs */}
      <ConfirmationDialog
        isOpen={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        onConfirm={confirmResetCode}
        title="Reset Code"
        message="Are you sure you want to reset your code? This will revert to the starter template."
        confirmText="Reset"
        variant="warning"
      />

      <ConfirmationDialog
        isOpen={retrieveDialogOpen}
        onClose={() => setRetrieveDialogOpen(false)}
        onConfirm={confirmRetrieveLastCode}
        title="Retrieve Last Code"
        message="Are you sure you want to restore your last saved code?"
        confirmText="Restore"
        variant="info"
      />

      <ConfirmationDialog
        isOpen={infoDialogOpen}
        onClose={() => setInfoDialogOpen(false)}
        onConfirm={() => setInfoDialogOpen(false)}
        title="Information"
        message={infoDialogMessage}
        confirmText="OK"
        cancelText=""
        variant="info"
      />
    </div>
  );
}