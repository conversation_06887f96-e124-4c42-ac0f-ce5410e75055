'use client';

import { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useTheme } from '@/context/ThemeContext';
import MonacoEditor, { editorThemes } from '@/components/coding/MonacoEditor';
import ConfirmationDialog from '@/components/coding/ConfirmationDialog';
import ThemeSelector from '@/components/coding/ThemeSelector';
import LanguageSelector from '@/components/coding/LanguageSelector';
import ExecutionResults from '@/components/coding/ExecutionResults';
import {
  PlayIcon,
  CheckIcon,
  ClockIcon,
  ChevronDownIcon,
  PlusIcon,
  TrashIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowPathIcon,
  ServerIcon,
  PaperAirplaneIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ExclamationCircleIcon,
  XCircleIcon,
  CheckCircleIcon,
  XMarkIcon,
  Cog6ToothIcon,
  ArrowUturnLeftIcon,
  DocumentDuplicateIcon,
  DocumentArrowDownIcon,
} from '@heroicons/react/24/outline';

interface Problem {
  _id: string;
  title: string;
  description: string;
  difficulty: string;
  topics: string[];
  constraints: string[];
  testCases: {
    input: string;
    output: string;
    explanation?: string;
  }[];
  examples?: {
    input: string;
    output: string;
    explanation?: string;
  }[];
}

interface CustomTestCase {
  input: string;
  output: string;
  explanation?: string;
}

interface Submission {
  _id: string;
  userId: string;
  problemId: string;
  code: string;
  language: string;
  results: {
    status: string;
    testResults?: Array<{
      input: string;
      expectedOutput: string;
      actualOutput: string;
      status: string;
      statusCode: number;
      error?: string;
      executionTime?: number;
      memory?: number;
    }>;
  };
  createdAt: string;
  updatedAt: string;
}

export default function ProblemDetail() {
  const params = useParams();
  const id = params?.id as string | undefined;
  const router = useRouter();
  const { theme } = useTheme();
  
  const [problem, setProblem] = useState<Problem | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-language') || 'JavaScript';
    }
    return 'JavaScript';
  });

  const [editorTheme, setEditorTheme] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('mockly-editor-theme') || (theme === 'dark' ? 'mockly-dark' : 'vs-light');
    }
    return 'mockly-dark';
  });

  const [submitting, setSubmitting] = useState(false);
  const [activeTestCase, setActiveTestCase] = useState(0);
  const [codeOptionsOpen, setCodeOptionsOpen] = useState(false);
  const [lastSavedCode, setLastSavedCode] = useState<string>('');
  const editorRef = useRef<any>(null);
  const initialLoadRef = useRef<boolean>(true);
  const [runningCode, setRunningCode] = useState(false);
  const [executionResults, setExecutionResults] = useState<any>(null);
  const [submissionResults, setSubmissionResults] = useState<any>(null);

  // Custom test cases
  const [customTestCases, setCustomTestCases] = useState<CustomTestCase[]>([]);
  const [showCustomTestCases, setShowCustomTestCases] = useState(false);
  const [activeCustomTestCase, setActiveCustomTestCase] = useState(0);
  const [usingCustomTestCase, setUsingCustomTestCase] = useState(false);

  // UI state
  const [activeTab, setActiveTab] = useState<'description' | 'submissions'>('description');
  const [testCaseTab, setTestCaseTab] = useState<'examples' | 'custom'>('examples');
  const [showTestCaseDetails, setShowTestCaseDetails] = useState(true);
  const [leftPanelWidth, setLeftPanelWidth] = useState(40); // percentage
  const [isResizing, setIsResizing] = useState(false);
  const [testCasePanelCollapsed, setTestCasePanelCollapsed] = useState(false);

  // Submissions state
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loadingSubmissions, setLoadingSubmissions] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<Submission | null>(null);

  // Dialog states
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [retrieveDialogOpen, setRetrieveDialogOpen] = useState(false);
  const [infoDialogOpen, setInfoDialogOpen] = useState(false);
  const [infoDialogMessage, setInfoDialogMessage] = useState('');

  // Default starter code templates based on language
  const getStarterCode = (lang: string) => {
    switch (lang) {
      case 'JavaScript':
        return `// Write your complete JavaScript program here
// Read input from stdin and write output to stdout

const readline = require('readline');
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

rl.on('line', (input) => {
    // Parse the input
    const data = input.trim();
    
    // Your solution logic here
    // Example: for addition problem
    // const numbers = data.split(' ').map(Number);
    // const result = numbers[0] + numbers[1];
    
    // Write your solution here
    const result = "Your output here";
    
    console.log(result);
    rl.close();
});`;

      case 'Python':
        return `# Write your complete Python program here
# Read input from stdin and write output to stdout

import sys

def main():
    # Read input
    input_line = input().strip()
    
    # Parse the input
    # Example: for addition problem
    # numbers = list(map(int, input_line.split()))
    # result = numbers[0] + numbers[1]
    
    # Your solution logic here
    result = "Your output here"
    
    # Print the result
    print(result)

if __name__ == "__main__":
    main()`;

      case 'Java':
        return `// Write your complete Java program here
// Read input from stdin and write output to stdout

import java.util.*;
import java.io.*;

public class Solution {
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        
        // Read input
        String inputLine = scanner.nextLine().trim();
        
        // Parse the input
        // Example: for addition problem
        // String[] parts = inputLine.split(" ");
        // int a = Integer.parseInt(parts[0]);
        // int b = Integer.parseInt(parts[1]);
        // int result = a + b;
        
        // Your solution logic here
        String result = "Your output here";
        
        // Print the result
        System.out.println(result);
        
        scanner.close();
    }
}`;

      case 'C++':
        return `// Write your complete C++ program here
// Read input from stdin and write output to stdout

#include <iostream>
#include <string>
#include <vector>
#include <sstream>
#include <algorithm>
#include <cmath>
#include <iomanip>

using namespace std;

int main() {
    string inputLine;
    getline(cin, inputLine);
    
    // Example implementation for Power(x, n) problem
    // Parse input like "x = 2.00000, n = 10"
    size_t xPos = inputLine.find("x = ");
    size_t nPos = inputLine.find(", n = ");
    
    if (xPos != string::npos && nPos != string::npos) {
        // Extract x and n values
        string xStr = inputLine.substr(xPos + 4, nPos - xPos - 4);
        string nStr = inputLine.substr(nPos + 6);
        
        double x = stod(xStr);
        int n = stoi(nStr);
        
        // Implement power function
        double result = 1.0;
        int absN = abs(n);
        
        for (int i = 0; i < absN; i++) {
            result *= x;
        }
        
        if (n < 0) {
            result = 1.0 / result;
        }
        
        cout << fixed << setprecision(5) << result << endl;
    } else {
        // For other input formats, implement your solution here
        cout << "Your output here" << endl;
    }
    
    return 0;
}`;

      default:
        return '// Write your solution here';
    }
  };

  // Sync editor theme with app theme
  useEffect(() => {
    const newTheme = theme === 'dark' ? 'mockly-dark' : 'vs-light';
    setEditorTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-theme', newTheme);
    }
  }, [theme]);

  // Handle language changes
  useEffect(() => {
    if (initialLoadRef.current) {
      const hasLoadedFromStorage = loadCodeFromLocalStorage();
      if (!hasLoadedFromStorage) {
        setCode(getStarterCode(language));
      }
      initialLoadRef.current = false;
    }
  }, [id]);

  const handleLanguageChange = (newLanguage: string) => {
    if (code.trim() && id) {
      try {
        localStorage.setItem(`code-${id}-${language}`, code);
        setLastSavedCode(code);
      } catch (error) {
        console.error('Error saving code before language change:', error);
      }
    }

    setLanguage(newLanguage);

    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-language', newLanguage);
    }

    setTimeout(() => {
      const savedCode = localStorage.getItem(`code-${id}-${newLanguage}`);
      if (savedCode) {
        setCode(savedCode);
        setLastSavedCode(savedCode);
      } else {
        setCode(getStarterCode(newLanguage));
      }
    }, 0);
  };

  const handleThemeChange = (newTheme: string) => {
    setEditorTheme(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('mockly-editor-theme', newTheme);
    }
  };

  useEffect(() => {
    const fetchProblem = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/problems/${id}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Problem not found');
          } else {
            const errorText = await response.text();
            throw new Error(`Error: ${response.status}`);
          }
        }

        const data = await response.json();
        setProblem(data);

        const hasLoadedFromStorage = loadCodeFromLocalStorage();
        if (!hasLoadedFromStorage) {
          setCode(getStarterCode(language));
        }
      } catch (error) {
        console.error('Error fetching problem:', error);
        setError(error instanceof Error ? error.message : 'Failed to load problem');
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchProblem();
    }
  }, [id]);

  // Fetch submissions when submissions tab is active
  useEffect(() => {
    if (activeTab === 'submissions') {
      fetchSubmissions();
    }
  }, [activeTab, id]);

  const handleRunCode = async () => {
    if (!code.trim()) {
      setExecutionResults({
        status: 'Compilation Error',
        statusCode: 6,
        error: 'Please write some code before running'
      });
      return;
    }

    setRunningCode(true);
    setExecutionResults(null);

    try {
      let testCaseToUse;
      
      if (usingCustomTestCase && customTestCases.length > 0) {
        testCaseToUse = customTestCases[activeCustomTestCase];
      } else if (problem?.testCases && problem.testCases.length > 0) {
        testCaseToUse = problem.testCases[activeTestCase];
      } else {
        throw new Error('No test case available');
      }

      // Run against all test cases (LeetCode style)
      const response = await fetch('/api/submissions/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code,
          language,
          problemId: id, // This will run against all test cases
        }),
      });

      if (!response.ok) {
        throw new Error(`Execution failed: ${response.status}`);
      }

      const result = await response.json();
      setExecutionResults(result.results);
    } catch (error) {
      console.error('Error running code:', error);
      setExecutionResults({
        status: 'Internal Error',
        statusCode: 8,
        error: error instanceof Error ? error.message : 'An error occurred while running your code'
      });
    } finally {
      setRunningCode(false);
    }
  };

  const handleSubmit = async () => {
    if (!code.trim()) {
      setSubmissionResults({
        status: 'Compilation Error',
        statusCode: 6,
        error: 'Please write some code before submitting'
      });
      return;
    }

    setSubmitting(true);
    setSubmissionResults(null);

    try {
      saveCodeToLocalStorage(code);

      const response = await fetch('/api/submissions/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          problemId: id,
          code,
          language,
        }),
      });

      if (!response.ok) {
        throw new Error(`Submission failed: ${response.status}`);
      }

      const result = await response.json();
      setSubmissionResults(result.results);
      
      // Refresh submissions if we're on the submissions tab
      if (activeTab === 'submissions') {
        fetchSubmissions();
      }
    } catch (error) {
      console.error('Error submitting solution:', error);
      setSubmissionResults({
        status: 'Internal Error',
        statusCode: 8,
        error: error instanceof Error ? error.message : 'An error occurred while submitting your solution'
      });
    } finally {
      setSubmitting(false);
    }
  };

  // Custom test case functions
  const addCustomTestCase = () => {
    setCustomTestCases([...customTestCases, { input: '', output: '', explanation: '' }]);
    setActiveCustomTestCase(customTestCases.length);
  };

  const removeCustomTestCase = (index: number) => {
    const newTestCases = customTestCases.filter((_, i) => i !== index);
    setCustomTestCases(newTestCases);
    if (activeCustomTestCase >= newTestCases.length) {
      setActiveCustomTestCase(Math.max(0, newTestCases.length - 1));
    }
  };

  const updateCustomTestCase = (index: number, field: keyof CustomTestCase, value: string) => {
    const newTestCases = [...customTestCases];
    newTestCases[index] = { ...newTestCases[index], [field]: value };
    setCustomTestCases(newTestCases);
  };

  // Fetch submissions for this problem
  const fetchSubmissions = async () => {
    if (!id) return;
    
    setLoadingSubmissions(true);
    try {
      const response = await fetch(`/api/submissions/problem/${id}`);
      if (response.ok) {
        const data = await response.json();
        setSubmissions(data);
      } else {
        console.error('Failed to fetch submissions');
      }
    } catch (error) {
      console.error('Error fetching submissions:', error);
    } finally {
      setLoadingSubmissions(false);
    }
  };

  // Save/Load functions
  const saveCodeToLocalStorage = (codeToSave: string) => {
    if (id) {
      try {
        localStorage.setItem(`code-${id}-${language}`, codeToSave);
        setLastSavedCode(codeToSave);
      } catch (error) {
        console.error('Error saving code to localStorage:', error);
      }
    }
  };

  const loadCodeFromLocalStorage = () => {
    if (id) {
      try {
        const savedCode = localStorage.getItem(`code-${id}-${language}`);
        if (savedCode) {
          setCode(savedCode);
          setLastSavedCode(savedCode);
          return true;
        }
      } catch (error) {
        console.error('Error loading code from localStorage:', error);
      }
    }
    return false;
  };

  // Code action functions
  const handleResetCode = () => {
    setResetDialogOpen(true);
    setCodeOptionsOpen(false);
  };

  const confirmResetCode = () => {
    setCode(getStarterCode(language));
  };

  const handleFormatCode = () => {
    if (editorRef.current) {
      editorRef.current.getAction('editor.action.formatDocument').run();
      setCodeOptionsOpen(false);
    }
  };

  const handleRetrieveLastCode = () => {
    if (lastSavedCode) {
      setRetrieveDialogOpen(true);
      setCodeOptionsOpen(false);
    } else {
      setInfoDialogMessage('No previously saved code found');
      setInfoDialogOpen(true);
      setCodeOptionsOpen(false);
    }
  };

  const confirmRetrieveLastCode = () => {
    setCode(lastSavedCode);
  };

  // Handle panel resizing
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const containerWidth = window.innerWidth;
      const newWidth = (e.clientX / containerWidth) * 100;
      
      // Constrain between 25% and 60%
      if (newWidth >= 25 && newWidth <= 60) {
        setLeftPanelWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+R to reload page
      if (event.ctrlKey && event.key === 'r') {
        event.preventDefault();
        window.location.reload();
        return;
      }

      // Ctrl+Enter to run code
      if (event.ctrlKey && event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (!runningCode) {
          handleRunCode();
        }
        return;
      }

      // Ctrl+Shift+Enter to submit code
      if (event.ctrlKey && event.shiftKey && event.key === 'Enter') {
        event.preventDefault();
        if (!submitting) {
          handleSubmit();
        }
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [runningCode, submitting]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen bg-gray-50 dark:bg-gray-950">
        <div className="h-10 w-10 animate-spin rounded-full border-4 border-gray-300 dark:border-gray-700 border-t-brand-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50 dark:bg-gray-950 text-center px-4">
        <div className="rounded-lg bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-8 max-w-md shadow-lg">
          <XMarkIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-red-600 dark:text-red-400 mb-4">Error</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-6">{error}</p>
          <button
            onClick={() => router.push('/coding/practice')}
            className="px-5 py-2.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors"
          >
            Go to Practice Problems
          </button>
        </div>
      </div>
    );
  }

  if (!problem) {
    return (
      <div className="flex flex-col items-center justify-center h-screen bg-gray-50 dark:bg-gray-950 text-center px-4">
        <div className="rounded-lg bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-800 p-8 max-w-md shadow-lg">
          <XMarkIcon className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-bold text-yellow-600 dark:text-yellow-400 mb-4">Problem Not Found</h2>
          <p className="text-gray-700 dark:text-gray-300 mb-6">
            The problem you're looking for doesn't exist or has been removed.
          </p>
          <button
            onClick={() => router.push('/coding/practice')}
            className="px-5 py-2.5 bg-brand-500 text-white rounded-md hover:bg-brand-600 transition-colors"
          >
            Go to Practice Problems
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen leetcode-bg-primary overflow-hidden">
      {/* LeetCode-Style Header */}
      <div className="flex items-center justify-between px-4 py-2 leetcode-bg-secondary border-b leetcode-border flex-shrink-0">
        {/* Left section - Navigation */}
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.push('/coding/practice')}
            className="flex items-center space-x-2 text-sm leetcode-text-secondary hover:leetcode-text-primary transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            <span>Problem List</span>
          </button>
          <div className="text-sm leetcode-text-muted">/</div>
          <span className="text-sm font-medium leetcode-text-primary">{problem.title}</span>
        </div>

        {/* Right section - Controls */}
        <div className="flex items-center space-x-2">
          <div className="hidden md:flex items-center space-x-2">
            <ThemeSelector
              selectedTheme={editorTheme}
              onThemeChange={handleThemeChange}
              themes={editorThemes}
            />
            <LanguageSelector
              selectedLanguage={language}
              onLanguageChange={handleLanguageChange}
              languages={['JavaScript', 'Python', 'Java', 'C++']}
            />
          </div>

          <button
            onClick={handleRunCode}
            disabled={runningCode}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 rounded transition-colors leetcode-button-hover"
          >
            {runningCode ? (
              <ArrowPathIcon className="h-4 w-4 mr-1.5 animate-spin" />
            ) : (
              <PlayIcon className="h-4 w-4 mr-1.5" />
            )}
            Run
          </button>
          <button
            onClick={handleSubmit}
            disabled={submitting}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-green-500 rounded transition-colors leetcode-button-hover"
          >
            {submitting ? (
              <ArrowPathIcon className="h-4 w-4 mr-1.5 animate-spin" />
            ) : (
              <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
              </svg>
            )}
            Submit
          </button>
        </div>
      </div>

      {/* LeetCode-Style Main Content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Left Panel - Problem Description */}
        <div
          className="flex flex-col leetcode-bg-secondary border-r leetcode-border"
          style={{ width: `${leftPanelWidth}%` }}
        >
          {/* Problem Header */}
          <div className="px-4 py-3 border-b leetcode-border">
            <div className="flex items-center justify-between">
              <h1 className="text-lg font-medium leetcode-text-primary">{problem.title}</h1>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs font-medium rounded ${
                  problem.difficulty === 'Easy' ? 'leetcode-easy bg-green-900/20' :
                  problem.difficulty === 'Medium' ? 'leetcode-medium bg-yellow-900/20' :
                  'leetcode-hard bg-red-900/20'
                }`}>
                  {problem.difficulty}
                </span>
              </div>
            </div>

            {/* Topics */}
            {problem.topics && problem.topics.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {problem.topics.slice(0, 4).map((topic, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 text-xs font-medium leetcode-text-muted bg-gray-700 rounded"
                  >
                    {topic}
                  </span>
                ))}
                {problem.topics.length > 4 && (
                  <span className="px-2 py-1 text-xs font-medium leetcode-text-muted bg-gray-700 rounded">
                    +{problem.topics.length - 4}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Problem Content */}
          <div className="flex-1 overflow-y-auto leetcode-scrollbar p-4">
            <div className="prose prose-sm prose-invert max-w-none">
              <div
                className="leetcode-text-secondary leading-relaxed"
                dangerouslySetInnerHTML={{ __html: problem.description }}
              />
            </div>
          </div>
        </div>

        {/* Resizer */}
        <div
          className="w-1 leetcode-bg-tertiary hover:bg-orange-500 cursor-col-resize transition-colors duration-200 relative group"
          onMouseDown={handleMouseDown}
        >
          <div className="absolute inset-y-0 -left-1 -right-1 group-hover:bg-orange-500/20 transition-colors duration-200" />
        </div>

        {/* Right Panel - Code Editor */}
        <div className="flex-1 flex flex-col leetcode-bg-primary">
          {/* Editor Header */}
          <div className="px-4 py-2 leetcode-bg-secondary border-b leetcode-border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium leetcode-text-primary">Code</span>
                <div className="flex items-center space-x-1">
                  <div className="w-3 h-3 rounded-full bg-red-500"></div>
                  <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                  <div className="w-3 h-3 rounded-full bg-green-500"></div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <div className="md:hidden flex items-center space-x-2">
                  <ThemeSelector
                    selectedTheme={editorTheme}
                    onThemeChange={handleThemeChange}
                    themes={editorThemes}
                  />
                  <LanguageSelector
                    selectedLanguage={language}
                    onLanguageChange={handleLanguageChange}
                    languages={['JavaScript', 'Python', 'Java', 'C++']}
                  />
                </div>
                <button
                  onClick={() => setCodeOptionsOpen(!codeOptionsOpen)}
                  className="p-1.5 leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors"
                >
                  <Cog6ToothIcon className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Code Editor */}
          <div className="flex-1 overflow-hidden relative">
            <MonacoEditor
              language={language}
              value={code}
              onChange={(value) => {
                setCode(value || '');
                if (value) saveCodeToLocalStorage(value);
              }}
              theme={editorTheme}
              height="100%"
            />

            {/* Code Options Dropdown */}
            {codeOptionsOpen && (
              <div className="absolute top-4 right-4 leetcode-bg-elevated rounded border leetcode-border shadow-lg py-1 z-10 min-w-48">
                <button
                  onClick={handleFormatCode}
                  className="w-full px-3 py-2 text-left text-sm leetcode-text-secondary hover:leetcode-text-primary hover:bg-gray-700 flex items-center"
                >
                  <DocumentArrowDownIcon className="h-4 w-4 mr-3" />
                  Format Code
                </button>
                <button
                  onClick={handleResetCode}
                  className="w-full px-3 py-2 text-left text-sm leetcode-text-secondary hover:leetcode-text-primary hover:bg-gray-700 flex items-center"
                >
                  <ArrowUturnLeftIcon className="h-4 w-4 mr-3" />
                  Reset to Template
                </button>
                <button
                  onClick={handleRetrieveLastCode}
                  className="w-full px-3 py-2 text-left text-sm leetcode-text-secondary hover:leetcode-text-primary hover:bg-gray-700 flex items-center"
                >
                  <DocumentDuplicateIcon className="h-4 w-4 mr-3" />
                  Restore Last Saved
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* LeetCode-Style Bottom Panel */}
      <div className="border-t leetcode-border leetcode-bg-secondary flex-shrink-0" style={{ height: testCasePanelCollapsed ? '48px' : '300px' }}>
        {/* Test Case Header */}
        <div className="flex items-center justify-between px-4 py-2 border-b leetcode-border">
          <div className="flex items-center space-x-1">
            <button
              onClick={() => {
                setTestCaseTab('examples');
                setUsingCustomTestCase(false);
              }}
              className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
                testCaseTab === 'examples'
                  ? 'leetcode-text-primary bg-gray-700'
                  : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
              }`}
            >
              Testcase
            </button>
            <button
              onClick={() => {
                setTestCaseTab('custom');
                setUsingCustomTestCase(true);
              }}
              className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
                testCaseTab === 'custom'
                  ? 'leetcode-text-primary bg-gray-700'
                  : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
              }`}
            >
              Custom ({customTestCases.length})
            </button>
          </div>

          <div className="flex items-center space-x-2">
            {testCaseTab === 'custom' && (
              <button
                onClick={addCustomTestCase}
                className="inline-flex items-center px-2 py-1 text-xs font-medium leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors"
              >
                <PlusIcon className="h-3 w-3 mr-1" />
                Add
              </button>
            )}
            <button
              onClick={() => setShowTestCaseDetails(!showTestCaseDetails)}
              className="p-1.5 leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors"
              title={showTestCaseDetails ? "Hide details" : "Show details"}
            >
              {showTestCaseDetails ? <EyeSlashIcon className="h-4 w-4" /> : <EyeIcon className="h-4 w-4" />}
            </button>
            <button
              onClick={() => setTestCasePanelCollapsed(!testCasePanelCollapsed)}
              className="p-1.5 leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700 rounded transition-colors"
              title={testCasePanelCollapsed ? "Expand panel" : "Collapse panel"}
            >
              <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${testCasePanelCollapsed ? 'rotate-180' : ''}`} />
            </button>
          </div>
        </div>

        {/* Test Case Content */}
        {!testCasePanelCollapsed && (
          <div className="flex-1 flex overflow-hidden">
            {/* Left Side - Test Cases */}
            <div className="w-1/2 border-r leetcode-border leetcode-bg-secondary flex flex-col">
              <div className="p-3 flex-1 overflow-y-auto leetcode-scrollbar">
                {testCaseTab === 'examples' && problem.testCases && problem.testCases.length > 0 && (
                  <div className="space-y-3">
                    {/* Test Case Selector */}
                    <div className="flex space-x-1 flex-wrap">
                      {problem.testCases.map((_, index) => (
                        <button
                          key={index}
                          onClick={() => setActiveTestCase(index)}
                          className={`px-3 py-1.5 text-sm font-medium rounded transition-colors ${
                            activeTestCase === index
                              ? 'leetcode-text-primary bg-gray-700'
                              : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
                          }`}
                        >
                          Case {index + 1}
                        </button>
                      ))}
                    </div>

                    {/* Test Case Details */}
                    {showTestCaseDetails && (
                      <div className="space-y-3">
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">Input:</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary overflow-x-auto whitespace-pre-wrap">
                              {problem.testCases[activeTestCase].input}
                            </pre>
                          </div>
                        </div>
                        <div>
                          <div className="text-xs font-medium leetcode-text-muted mb-1">Output:</div>
                          <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                            <pre className="text-sm font-mono leetcode-text-primary overflow-x-auto whitespace-pre-wrap">
                              {problem.testCases[activeTestCase].output}
                            </pre>
                          </div>
                        </div>
                        {problem.testCases[activeTestCase].explanation && (
                          <div>
                            <div className="text-xs font-medium leetcode-text-muted mb-1">Explanation:</div>
                            <div className="leetcode-bg-tertiary rounded p-2 border leetcode-border">
                              <div className="text-sm leetcode-text-secondary">
                                {problem.testCases[activeTestCase].explanation}
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {testCaseTab === 'custom' && (
                  <div className="space-y-4">
                    {customTestCases.length === 0 ? (
                      <div className="text-center py-12">
                        <PlusIcon className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                        <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">No custom test cases yet</p>
                        <button
                          onClick={addCustomTestCase}
                          className="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-colors border border-blue-200 dark:border-blue-800"
                        >
                          <PlusIcon className="h-4 w-4 mr-2" />
                          Create Your First Test Case
                        </button>
                      </div>
                    ) : (
                      <>
                        {/* Custom test case selector */}
                        <div className="flex space-x-2 flex-wrap">
                          {customTestCases.map((_, index) => (
                            <div key={index} className="flex items-center">
                              <button
                                onClick={() => setActiveCustomTestCase(index)}
                                className={`px-3 py-2 text-sm font-medium rounded-l-lg transition-colors border ${
                                  activeCustomTestCase === index
                                    ? 'bg-blue-600 text-white border-blue-600 shadow-sm'
                                    : 'bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700'
                                }`}
                              >
                                Custom {index + 1}
                              </button>
                              <button
                                onClick={() => removeCustomTestCase(index)}
                                className={`px-2 py-2 text-sm rounded-r-lg border-l-0 border transition-colors ${
                                  activeCustomTestCase === index
                                    ? 'bg-blue-700 text-white border-blue-600 hover:bg-blue-800'
                                    : 'bg-gray-50 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400'
                                }`}
                              >
                                <TrashIcon className="h-3 w-3" />
                              </button>
                            </div>
                          ))}
                        </div>

                        {/* Custom test case editor */}
                        {showTestCaseDetails && customTestCases[activeCustomTestCase] && (
                          <div className="rounded-lg bg-gray-50 dark:bg-gray-800 p-4 border border-gray-200 dark:border-gray-700">
                            <div className="space-y-4">
                              <div>
                                <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                                  Input:
                                </label>
                                <textarea
                                  value={customTestCases[activeCustomTestCase].input}
                                  onChange={(e) => updateCustomTestCase(activeCustomTestCase, 'input', e.target.value)}
                                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-3 py-3 text-sm font-mono text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none"
                                  rows={2}
                                  placeholder="Enter test input..."
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                                  Expected Output:
                                </label>
                                <textarea
                                  value={customTestCases[activeCustomTestCase].output}
                                  onChange={(e) => updateCustomTestCase(activeCustomTestCase, 'output', e.target.value)}
                                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-3 py-3 text-sm font-mono text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none"
                                  rows={2}
                                  placeholder="Enter expected output..."
                                />
                              </div>
                              <div>
                                <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
                                  Explanation (optional):
                                </label>
                                <textarea
                                  value={customTestCases[activeCustomTestCase].explanation || ''}
                                  onChange={(e) => updateCustomTestCase(activeCustomTestCase, 'explanation', e.target.value)}
                                  className="w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-900 px-3 py-3 text-sm text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 focus:outline-none"
                                  rows={2}
                                  placeholder="Explain this test case..."
                                />
                              </div>
                            </div>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>

            {/* Right Side - Results */}
            <div className="w-1/2 leetcode-bg-primary flex flex-col">
              <div className="p-3 flex-1 overflow-y-auto leetcode-scrollbar">
                {/* Execution Results */}
                {executionResults && (
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <PlayIcon className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm font-medium leetcode-text-primary">Run Results</span>
                    </div>
                    <ExecutionResults results={executionResults} isSubmission={false} />
                  </div>
                )}

                {/* Submission Results */}
                {submissionResults && (
                  <div className="mb-4">
                    <div className="flex items-center mb-2">
                      <CheckIcon className="h-4 w-4 mr-2 text-green-500" />
                      <span className="text-sm font-medium leetcode-text-primary">Submission Results</span>
                    </div>
                    <ExecutionResults results={submissionResults} isSubmission={true} />
                  </div>
                )}

                {/* Default State */}
                {!executionResults && !submissionResults && (
                  <div className="text-center py-8">
                    <div className="leetcode-text-muted mb-2">
                      <svg className="h-8 w-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-sm leetcode-text-muted">You must run your code first</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Confirmation Dialogs */}
      <ConfirmationDialog
        isOpen={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        onConfirm={confirmResetCode}
        title="Reset Code"
        message="Are you sure you want to reset your code? This will revert to the starter template."
        confirmText="Reset"
        variant="warning"
      />

      <ConfirmationDialog
        isOpen={retrieveDialogOpen}
        onClose={() => setRetrieveDialogOpen(false)}
        onConfirm={confirmRetrieveLastCode}
        title="Retrieve Last Code"
        message="Are you sure you want to restore your last saved code?"
        confirmText="Restore"
        variant="info"
      />

      <ConfirmationDialog
        isOpen={infoDialogOpen}
        onClose={() => setInfoDialogOpen(false)}
        onConfirm={() => setInfoDialogOpen(false)}
        title="Information"
        message={infoDialogMessage}
        confirmText="OK"
        cancelText=""
        variant="info"
      />
    </div>
  );
}