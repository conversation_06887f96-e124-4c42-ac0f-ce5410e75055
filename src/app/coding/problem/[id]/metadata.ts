import { Metadata } from "next";

export const generateMetadata = async ({ params }: { params: { id: string } }): Promise<Metadata> => {
  try {
    // Fetch the problem data to get the title
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/problems/${params.id}`);
    
    if (!response.ok) {
      return {
        title: "Problem | Mockly",
      };
    }
    
    const problem = await response.json();
    
    return {
      title: `${problem.title} | Problem`,
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Problem | Mockly",
    };
  }
};

