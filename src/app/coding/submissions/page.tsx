'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import PageBreadcrumb from "@/components/common/PageBreadCrumb";
import { format } from 'date-fns';

interface CodingSubmission {
  _id: string;
  problemId: string;
  problemTitle?: string;
  code: string;
  language: string;
  results: {
    status: 'Accepted' | 'Wrong Answer' | 'Time Limit Exceeded' | string;
  };
  createdAt: string;
}

export default function SubmissionHistory() {
  const [submissions, setSubmissions] = useState<CodingSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchSubmissions = async () => {
      try {
        const response = await fetch('/api/submissions/history');
        if (response.ok) {
          const data = await response.json();
          setSubmissions(data);
        }
      } catch (error) {
        console.error('Error fetching submissions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubmissions();
  }, []);

  const handleSubmissionClick = (submissionId: string) => {
    router.push(`/coding/submission/${submissionId}`);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'M/d/yyyy, h:mm:ss a');
  };

  return (
    <div>
      <PageBreadcrumb pageTitle="Submission History" />
      
      {loading ? (
        <div className="flex justify-center py-10">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-brand-500"></div>
        </div>
      ) : (
        <div className="rounded-2xl border border-gray-200 bg-white/[0.03] p-5 dark:border-gray-800 md:p-6">
          <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white/90">
            Recent Coding Submissions
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-800">
                  <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Problem</th>
                  <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Status</th>
                  <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Language</th>
                  <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Submitted</th>
                </tr>
              </thead>
              <tbody>
                {submissions.map((submission) => (
                  <tr 
                    key={submission._id} 
                    className="cursor-pointer border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-white/[0.02]"
                    onClick={() => handleSubmissionClick(submission._id)}
                  >
                    <td className="py-4">
                      <div>
                        <span className="text-sm font-medium text-gray-800 dark:text-white/90">
                          {submission.problemTitle || 'Unknown Problem'}
                        </span>
                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                          {submission.problemId}
                        </span>
                      </div>
                    </td>
                    <td className="py-4">
                      <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                        submission.results.status === 'Accepted' 
                          ? 'bg-green-100 text-green-700 dark:bg-green-500/20 dark:text-green-400'
                          : submission.results.status === 'Wrong Answer'
                          ? 'bg-red-100 text-red-700 dark:bg-red-500/20 dark:text-red-400'
                          : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-500/20 dark:text-yellow-400'
                      }`}>
                        {submission.results.status}
                      </span>
                    </td>
                    <td className="py-4 text-sm text-gray-800 dark:text-white/90">
                      {submission.language}
                    </td>
                    <td className="py-4 text-sm text-gray-500 dark:text-gray-400">
                      {formatDate(submission.createdAt)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
