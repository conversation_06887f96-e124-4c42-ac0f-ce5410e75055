'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import {
  PlusIcon,
  MinusIcon,
  CodeBracketIcon,
  PhotoIcon,
  DocumentTextIcon,
  AcademicCapIcon,
  TagIcon,
  ExclamationTriangleIcon,
  BeakerIcon,
  XMarkIcon,
  ArrowLeftIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import { toast } from "@/components/ui/toast/use-toast"

export default function CreateQuestionPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  // Redirect non-admin users
  useEffect(() => {
    if (status !== 'loading' && (!session || session.user?.role !== 'admin')) {
      router.push('/coding')
    }
  }, [session, status, router])

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [activeTab, setActiveTab] = useState('basic')
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    difficulty: 'Easy',
    topics: [] as string[],
    constraints: [] as string[], // Change from string to string array
    images: [] as File[],
    testCases: [{ input: '', output: '', explanation: '' }] // Changed from examples to testCases
  })

  // Add a function to handle constraints
  const handleConstraintChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Split by new lines and filter out empty lines
    const constraintArray = e.target.value
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0);

    setFormData(prev => ({ ...prev, constraints: constraintArray }));
  }

  // Topic options for MultiSelect
  const topicOptions = [
    { value: "Arrays", text: "Arrays", selected: false },
    { value: "Strings", text: "Strings", selected: false },
    { value: "Hash Table", text: "Hash Table", selected: false },
    { value: "Dynamic Programming", text: "Dynamic Programming", selected: false },
    { value: "Math", text: "Math", selected: false },
    { value: "Sorting", text: "Sorting", selected: false },
    { value: "Greedy", text: "Greedy", selected: false },
    { value: "Depth-First Search", text: "Depth-First Search", selected: false },
    { value: "Binary Search", text: "Binary Search", selected: false },
    { value: "Tree", text: "Tree", selected: false },
    { value: "Graph", text: "Graph", selected: false },
    { value: "Linked List", text: "Linked List", selected: false },
    { value: "Two Pointers", text: "Two Pointers", selected: false },
    { value: "Heap", text: "Heap", selected: false },
    { value: "Recursion", text: "Recursion", selected: false }
  ]

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleTestCaseChange = (index: number, field: string, value: string) => {
    const updatedTestCases = [...formData.testCases]
    updatedTestCases[index] = { ...updatedTestCases[index], [field]: value }
    setFormData(prev => ({ ...prev, testCases: updatedTestCases }))
  }

  const addTestCase = () => {
    setFormData(prev => ({
      ...prev,
      testCases: [...prev.testCases, { input: '', output: '', explanation: '' }]
    }))
  }

  const removeTestCase = (index: number) => {
    const updatedTestCases = formData.testCases.filter((_, i) => i !== index)
    setFormData(prev => ({ ...prev, testCases: updatedTestCases }))
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newImages = Array.from(e.target.files)
      setFormData(prev => ({ ...prev, images: [...prev.images, ...newImages] }))
    }
  }

  const removeImage = (index: number) => {
    const updatedImages = formData.images.filter((_, i) => i !== index)
    setFormData(prev => ({ ...prev, images: updatedImages }))
  }

  // Update the handleSubmit function to validate all required fields
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Comprehensive validation for all required fields
    const validationErrors = [];

    // Basic tab validation
    if (!formData.title.trim()) {
      validationErrors.push('Problem title is required');
    }

    if (!formData.difficulty) {
      validationErrors.push('Difficulty level is required');
    }

    if (formData.topics.length === 0) {
      validationErrors.push('At least one topic is required');
    }

    // Details tab validation
    if (!formData.description.trim()) {
      validationErrors.push('Problem description is required');
    }

    if (formData.constraints.length === 0) {
      validationErrors.push('At least one constraint is required');
    }

    // Test cases tab validation
    if (formData.testCases.length === 0) {
      validationErrors.push('At least one test case is required');
    } else {
      // Check if any test case has empty input or output
      const hasEmptyTestCase = formData.testCases.some(tc => !tc.input.trim() || !tc.output.trim());
      if (hasEmptyTestCase) {
        validationErrors.push('All test cases must have both input and output');
      }
    }

    // If there are validation errors, show them and return
    if (validationErrors.length > 0) {
      validationErrors.forEach(error => toast({ description: error }));
      return;
    }

    // Continue with form submission
    setIsSubmitting(true);

    try {
      // Create FormData for file uploads
      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title);
      formDataToSend.append('description', formData.description);
      formDataToSend.append('difficulty', formData.difficulty);
      formDataToSend.append('constraints', JSON.stringify(formData.constraints));
      formDataToSend.append('topics', JSON.stringify(formData.topics));
      formDataToSend.append('testCases', JSON.stringify(formData.testCases));

      // Append images
      formData.images.forEach((image, index) => {
        formDataToSend.append(`image-${index}`, image);
      });

      console.log('Submitting problem data to API...');

      // Submit to API
      const response = await fetch('/api/problems/create', {
        method: 'POST',
        body: formDataToSend,
      });

      const responseData = await response.json();

      if (!response.ok) {
        console.error('API error response:', responseData);
        throw new Error(responseData.message || responseData.error || 'Failed to create problem');
      }

      console.log('Problem created successfully:', responseData);

      // Handle success with green toast
      toast({
        description: 'Problem created successfully!',
        variant: 'success' // Use success variant for green color
      });

      setTimeout(() => {
        router.push('/coding/practice');
      }, 1500);
    } catch (error) {
      console.error('Error creating problem:', error);
      toast({
        description: `Failed to create problem: ${error instanceof Error ? error.message : 'Unknown error'}`,
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Create a separate function for tab navigation
  const navigateToTab = (tabId: string) => {
    // Current tab validation
    if (activeTab === 'basic' && tabId !== 'basic') {
      // Validate basic tab fields before allowing navigation
      if (!formData.title.trim()) {
        toast({
          description: 'Please provide a problem title',
          variant: 'destructive'
        });
        return;
      }

      if (formData.topics.length === 0) {
        toast({
          description: 'Please select at least one topic',
          variant: 'destructive'
        });
        return;
      }

      if (!formData.difficulty) {
        toast({
          description: 'Please select a difficulty level',
          variant: 'destructive'
        });
        return;
      }
    }

    if (activeTab === 'details' && tabId !== 'basic' && tabId !== 'details') {
      // Validate details tab fields before allowing navigation
      if (!formData.description.trim()) {
        toast({
          description: 'Please provide a problem description',
          variant: 'destructive'
        });
        return;
      }

      if (formData.constraints.length === 0) {
        toast({
          description: 'Please provide at least one constraint',
          variant: 'destructive'
        });
        return;
      }
    }

    if (activeTab === 'testCases' && tabId !== 'basic' && tabId !== 'details' && tabId !== 'testCases') {
      // Validate test cases tab fields before allowing navigation
      if (formData.testCases.length === 0) {
        toast({
          description: 'Please add at least one test case',
          variant: 'destructive'
        });
        return;
      }

      const hasEmptyTestCase = formData.testCases.some(tc => !tc.input.trim() || !tc.output.trim());
      if (hasEmptyTestCase) {
        toast({
          description: 'Please fill in both input and output for all test cases',
          variant: 'destructive'
        });
        return;
      }
    }

    // If validation passes, navigate to the requested tab
    setActiveTab(tabId);
  };

  if (status === 'loading') {
    return (
      <div className="container mx-auto p-6 flex justify-center items-center min-h-[60vh]">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-gray-300 border-t-brand-500"></div>
      </div>
    )
  }

  if (status === 'authenticated' && session?.user?.role === 'admin') {
    const tabs = [
      {
        id: 'basic',
        label: 'Basic Information',
        icon: <DocumentTextIcon className="h-5 w-5" />
      },
      {
        id: 'details',
        label: 'Problem Details',
        icon: <BeakerIcon className="h-5 w-5" />
      },
      {
        id: 'testCases',
        label: 'Test Cases',
        icon: <CodeBracketIcon className="h-5 w-5" />
      },
      {
        id: 'images',
        label: 'Images',
        icon: <PhotoIcon className="h-5 w-5" />
      }
    ];

    return (
      <div className="container mx-auto p-6">
        <div className="mb-8 flex items-center">
          <CodeBracketIcon className="h-8 w-8 text-brand-500 mr-3" />
          <h1 className="text-3xl font-bold text-gray-800 dark:text-white">Create Coding Problem</h1>
        </div>

        {/* Form with thin border and subtle shadow */}
        <div className="border border-gray-200 dark:border-gray-700 rounded-xl shadow-sm bg-white dark:bg-gray-900 p-6">
          <form
            onSubmit={(e) => {
              e.preventDefault(); // Always prevent default form submission
              if (activeTab === 'images') {
                handleSubmit(e);
              }
            }}
            className="bg-white dark:bg-gray-900 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
          >
            {/* Header section */}
            <div className="bg-gray-100 dark:bg-gray-800 rounded-t-xl border-b border-gray-200 dark:border-gray-700">
              <div className="container mx-auto px-6 py-8">
                <div className="flex items-center gap-4">
                  <div className="bg-gray-200 dark:bg-gray-700 p-3 rounded-lg">
                    <CodeBracketIcon className="h-6 w-6 text-gray-700 dark:text-gray-300" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-800 dark:text-white">Problem Creator</h1>
                    <p className="text-gray-600 dark:text-gray-400 mt-1">Design a challenging coding problem for your students</p>
                  </div>
                </div>
              </div>

              {/* Keep only this tab navigation and remove the other one */}
              <div className="bg-gray-50 dark:bg-gray-800 px-6 py-2 border-b border-gray-200 dark:border-gray-700">
                <nav className="flex space-x-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      type="button" // Explicitly set type to button
                      onClick={() => navigateToTab(tab.id)}
                      className={`px-4 py-3 rounded-t-lg flex items-center gap-2 text-sm font-medium transition-colors ${
                        activeTab === tab.id
                          ? 'bg-white dark:bg-gray-900 text-brand-600 dark:text-brand-400 shadow-sm'
                          : 'text-gray-600 dark:text-gray-300 hover:bg-white/50 dark:hover:bg-gray-700/50'
                      }`}
                    >
                      {tab.icon}
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Form content */}
            <div className="p-6">
              {/* Basic Info Tab */}
              {activeTab === 'basic' && (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="col-span-1">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        <div className="flex items-center">
                          <span className="mr-2">Problem Title</span>
                          <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full dark:bg-gray-700 dark:text-gray-400">Required</span>
                        </div>
                      </label>
                      <input
                        type="text"
                        name="title"
                        value={formData.title}
                        onChange={handleChange}
                        className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-sm focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        placeholder="e.g., Two Sum"
                        required
                      />
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Descriptive title for the problem</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center">
                        <span className="mr-2">Difficulty Level</span>
                        <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full dark:bg-gray-700 dark:text-gray-400">Required</span>
                      </div>
                    </label>
                    <div className="grid grid-cols-3 gap-4">
                      {['Easy', 'Medium', 'Hard'].map((level) => (
                        <div key={level} className="relative">
                          <input
                            type="radio"
                            id={`difficulty-${level}`}
                            name="difficulty"
                            value={level}
                            checked={formData.difficulty === level}
                            onChange={handleChange}
                            className="peer absolute h-0 w-0 opacity-0"
                          />
                          <label
                            htmlFor={`difficulty-${level}`}
                            className={`flex items-center justify-center p-3 rounded-lg border cursor-pointer transition-all peer-checked:ring-2 peer-checked:ring-offset-2 ${level === 'Easy'
                                ? 'bg-green-50 border-green-200 text-green-700 peer-checked:border-green-500 peer-checked:ring-green-500 dark:bg-green-900/20 dark:border-green-800 dark:text-green-400'
                                : level === 'Medium'
                                  ? 'bg-yellow-50 border-yellow-200 text-yellow-700 peer-checked:border-yellow-500 peer-checked:ring-yellow-500 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-400'
                                  : 'bg-red-50 border-red-200 text-red-700 peer-checked:border-red-500 peer-checked:ring-red-500 dark:bg-red-900/20 dark:border-red-800 dark:text-red-400'
                              }`}
                          >
                            <AcademicCapIcon className="h-5 w-5 mr-2" />
                            {level}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center">
                        <TagIcon className="h-4 w-4 mr-1" />
                        <span className="mr-2">Topics</span>
                        <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full dark:bg-gray-700 dark:text-gray-400">Required</span>
                      </div>
                    </label>

                    {/* Topics input field */}
                    <div className="relative rounded-lg border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm">
                      <div className="flex flex-wrap items-center p-2 gap-2">
                        {formData.topics.map(topic => (
                          <div key={topic}
                            className="flex items-center bg-indigo-500/20 text-indigo-700 dark:text-indigo-200 px-3 py-1.5 rounded-full border border-indigo-500/30"
                          >
                            <span>{topic}</span>
                            <button
                              onClick={() => setFormData({ ...formData, topics: formData.topics.filter(t => t !== topic) })}
                              className="ml-2 text-indigo-600 dark:text-indigo-300 hover:text-indigo-800 dark:hover:text-indigo-100 transition-colors"
                              type="button"
                            >
                              <XMarkIcon className="h-3.5 w-3.5" />
                            </button>
                          </div>
                        ))}

                        <input
                          type="text"
                          className="flex-grow min-w-[120px] p-1 bg-transparent border-none focus:ring-0 focus:outline-none text-sm text-gray-700 dark:text-gray-300"
                          placeholder={formData.topics.length === 0 ? "Select or type topics and press Enter" : "Add more topics..."}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && e.currentTarget.value.trim()) {
                              e.preventDefault();
                              const newTopic = e.currentTarget.value.trim();
                              if (!formData.topics.includes(newTopic)) {
                                setFormData({ ...formData, topics: [...formData.topics, newTopic] });
                              }
                              e.currentTarget.value = '';
                            }
                          }}
                        />
                      </div>
                    </div>

                    {/* Suggested topics */}
                    <div className="mt-3">
                      <p className="text-xs text-gray-500 dark:text-gray-400 mb-1">Suggested topics:</p>
                      <div className="flex flex-wrap gap-2">
                        {topicOptions.map(topic => (
                          !formData.topics.includes(topic.value) && (
                            <button
                              key={topic.value}
                              type="button"
                              onClick={() => setFormData({ ...formData, topics: [...formData.topics, topic.value] })}
                              className="px-2.5 py-1 text-xs bg-gray-100 text-gray-700 rounded-full hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 transition-colors"
                            >
                              {topic.text}
                            </button>
                          )
                        ))}
                      </div>
                    </div>
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Select at least one topic relevant to the problem</p>
                  </div>
                </div>
              )}

              {/* Problem Details Tab */}
              {activeTab === 'details' && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center">
                        <DocumentTextIcon className="h-4 w-4 mr-1" />
                        <span className="mr-2">Problem Description</span>
                        <span className="text-xs px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full dark:bg-gray-700 dark:text-gray-400">Required</span>
                      </div>
                    </label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleChange}
                      rows={10}
                      className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-sm focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 dark:bg-gray-700 dark:border-gray-600 dark:text-white font-mono"
                      placeholder="Provide a detailed description of the problem, including constraints and requirements. Markdown is supported."
                      required
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">Markdown formatting is supported</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      <div className="flex items-center">
                        <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                        <span className="mr-2">Constraints</span>
                      </div>
                    </label>
                    <textarea
                      name="constraints"
                      value={formData.constraints.join('\n')}
                      onChange={handleConstraintChange}
                      rows={5}
                      className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-sm focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 dark:bg-gray-700 dark:border-gray-600 dark:text-white font-mono"
                      placeholder="Specify constraints for the problem (e.g., 1 <= n <= 10^5)"
                    />
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">List each constraint on a new line</p>
                  </div>
                </div>
              )}

              {/* Test Cases Tab */}
              {activeTab === 'testCases' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-800 dark:text-white">Test Cases</h3>
                    <button
                      type="button" // Explicitly set type to button
                      onClick={addTestCase}
                      className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-md text-brand-600 bg-brand-50 hover:bg-brand-100 dark:text-brand-400 dark:bg-brand-900/20 dark:hover:bg-brand-900/30"
                    >
                      <PlusIcon className="h-4 w-4 mr-1" />
                      Add Test Case
                    </button>
                  </div>

                  {formData.testCases.map((testCase, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex justify-between items-center mb-3">
                        <h4 className="font-medium text-gray-700 dark:text-gray-300">Test Case {index + 1}</h4>
                        {formData.testCases.length > 1 && (
                          <button
                            type="button" // Explicitly set type to button
                            onClick={() => removeTestCase(index)}
                            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <MinusIcon className="h-4 w-4" />
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Input</label>
                          <textarea
                            value={testCase.input}
                            onChange={(e) => handleTestCaseChange(index, 'input', e.target.value)}
                            className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-sm focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 dark:bg-gray-700 dark:border-gray-600 dark:text-white font-mono"
                            rows={3}
                            placeholder="Input for test case"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Output</label>
                          <textarea
                            value={testCase.output}
                            onChange={(e) => handleTestCaseChange(index, 'output', e.target.value)}
                            className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-sm focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 dark:bg-gray-700 dark:border-gray-600 dark:text-white font-mono"
                            rows={3}
                            placeholder="Expected output"
                          />
                        </div>

                        <div className="md:col-span-2">
                          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Explanation</label>
                          <textarea
                            value={testCase.explanation}
                            onChange={(e) => handleTestCaseChange(index, 'explanation', e.target.value)}
                            className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-sm focus:border-brand-500 focus:ring-4 focus:ring-brand-500/20 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            rows={2}
                            placeholder="Explain how the output is derived from the input"
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Images Tab */}
              {activeTab === 'images' && (
                <div className="space-y-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <PhotoIcon className="h-5 w-5 text-brand-500 mr-2" />
                      <h3 className="text-lg font-medium text-gray-800 dark:text-white">Upload Images</h3>
                    </div>

                    {formData.images.length > 0 && (
                      <div className="flex items-center gap-3">
                        <button
                          type="button"
                          onClick={() => setFormData({ ...formData, images: [] })}
                          className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700 transition-colors"
                        >
                          <XMarkIcon className="h-3.5 w-3.5 mr-1" />
                          Clear all
                        </button>

                        <label className="relative">
                          <span className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md text-white bg-brand-500 hover:bg-brand-600 shadow-sm cursor-pointer transition-colors">
                            <PlusIcon className="h-3.5 w-3.5 mr-1" />
                            Add More
                          </span>
                          <input
                            type="file"
                            name="images"
                            onChange={handleImageChange}
                            className="sr-only"
                            multiple
                            accept="image/*"
                          />
                        </label>
                      </div>
                    )}
                  </div>

                  {formData.images.length === 0 ? (
                    <div className="border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-xl p-6 transition-all hover:border-brand-400 dark:hover:border-brand-600">
                      <div className="flex flex-col items-center justify-center space-y-3">
                        <PhotoIcon className="h-10 w-10 text-gray-400 dark:text-gray-500" />
                        <div className="text-center">
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300">Upload images related to the problem</p>
                          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">PNG, JPG, GIF up to 5MB</p>
                        </div>

                        <label className="relative">
                          <span className="inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md text-white bg-brand-500 hover:bg-brand-600 shadow-sm cursor-pointer">
                            <PlusIcon className="h-4 w-4 mr-1" />
                            Choose Files
                          </span>
                          <input
                            type="file"
                            name="images"
                            onChange={handleImageChange}
                            className="sr-only"
                            multiple
                            accept="image/*"
                          />
                        </label>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        {formData.images.map((image, index) => (
                          <div key={index} className="relative group rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-sm">
                            <img
                              src={URL.createObjectURL(image)}
                              alt={`Uploaded image ${index + 1}`}
                              className="w-full h-40 object-cover"
                            />
                            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                              <button
                                type="button"
                                onClick={() => removeImage(index)}
                                className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                                aria-label="Remove image"
                              >
                                <XMarkIcon className="h-5 w-5" />
                              </button>
                            </div>
                            <div className="p-2 text-xs text-gray-500 dark:text-gray-400 truncate">
                              {image.name}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Form footer with navigation buttons */}
            <div className="flex justify-between items-center pt-6 border-t border-gray-200 dark:border-gray-700 mt-8 px-6 pb-6">
              <button
                type="button"
                onClick={() => {
                  const tabs = ['basic', 'details', 'testCases', 'images'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex > 0) {
                    navigateToTab(tabs[currentIndex - 1]);
                  }
                }}
                className={`px-5 py-2.5 rounded-lg text-sm font-medium transition-colors ${activeTab === 'basic'
                    ? 'opacity-0 pointer-events-none' // Hide but preserve layout
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 shadow-sm dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700 dark:hover:bg-gray-700'
                  }`}
              >
                <span className="flex items-center">
                  <ArrowLeftIcon className="h-4 w-4 mr-2" />
                  Previous
                </span>
              </button>

              <button
                type="button"
                onClick={activeTab === 'images' ? handleSubmit : () => {
                  const tabs = ['basic', 'details', 'testCases', 'images'];
                  const currentIndex = tabs.indexOf(activeTab);
                  if (currentIndex < tabs.length - 1) {
                    navigateToTab(tabs[currentIndex + 1]);
                  }
                }}
                disabled={isSubmitting}
                className="px-5 py-2.5 rounded-lg text-sm font-medium bg-brand-600 text-white hover:bg-brand-700 shadow-sm transition-colors dark:bg-brand-500 dark:hover:bg-brand-600"
              >
                <span className="flex items-center">
                  {activeTab === 'images' ? (
                    isSubmitting ? (
                      <>
                        <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent mr-2"></div>
                        Submitting...
                      </>
                    ) : (
                      'Create Problem'
                    )
                  ) : (
                    <>
                      Next
                      <ArrowRightIcon className="h-4 w-4 ml-2" />
                    </>
                  )}
                </span>
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6">
      <div className="bg-red-50 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-sm dark:bg-red-900/20 dark:border-red-800 dark:text-red-400">
        <div className="flex">
          <div className="py-1">
            <svg className="h-6 w-6 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div>
            <p className="font-medium">Access Denied</p>
            <p className="text-sm">You need admin privileges to access this page.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
