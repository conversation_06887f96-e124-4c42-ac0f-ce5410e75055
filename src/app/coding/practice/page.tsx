'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  CheckCircleIcon,
  ClockIcon,
  CodeBracketIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon
} from '@heroicons/react/24/outline';

interface Problem {
  _id: string;
  title: string;
  description: string;
  difficulty: 'Easy' | 'Medium' | 'Hard';
  topics: string[];
  createdAt: string;
}

interface Submission {
  problemId: string;
  results: {
    status: string;
  };
}

export default function PracticeProblems() {
  const [problems, setProblems] = useState<Problem[]>([]);
  const [submissions, setSubmissions] = useState<Submission[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [difficultyFilter, setDifficultyFilter] = useState<string>('');
  const [topicFilter, setTopicFilter] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const router = useRouter();
  const { data: session } = useSession();

  const completedProblems = submissions.filter(sub => sub.results.status === 'Accepted');
  const allTopics = [...new Set(problems.flatMap(p => p.topics))].sort();

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch problems and submissions in parallel
        const [problemsResponse, submissionsResponse] = await Promise.all([
          fetch('/api/problems'),
          fetch('/api/submissions/history')
        ]);

        if (problemsResponse.ok) {
          const problemsData = await problemsResponse.json();
          setProblems(problemsData);
        }

        if (submissionsResponse.ok) {
          const submissionsData = await submissionsResponse.json();
          setSubmissions(submissionsData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredProblems = problems.filter(problem => {
    const matchesSearch = problem.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         problem.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesDifficulty = !difficultyFilter || problem.difficulty === difficultyFilter;
    const matchesTopic = !topicFilter || problem.topics.includes(topicFilter);
    
    return matchesSearch && matchesDifficulty && matchesTopic;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy':
        return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20';
      case 'Medium':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20';
      case 'Hard':
        return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20';
    }
  };

  const isProblemCompleted = (problemId: string) => {
    return completedProblems.some(sub => sub.problemId === problemId);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setDifficultyFilter('');
    setTopicFilter('');
  };

  const getProgressStats = () => {
    const total = problems.length;
    const completed = completedProblems.length;
    const easy = problems.filter(p => p.difficulty === 'Easy').length;
    const medium = problems.filter(p => p.difficulty === 'Medium').length;
    const hard = problems.filter(p => p.difficulty === 'Hard').length;
    const completedEasy = completedProblems.filter(sub => {
      const problem = problems.find(p => p._id === sub.problemId);
      return problem?.difficulty === 'Easy';
    }).length;
    const completedMedium = completedProblems.filter(sub => {
      const problem = problems.find(p => p._id === sub.problemId);
      return problem?.difficulty === 'Medium';
    }).length;
    const completedHard = completedProblems.filter(sub => {
      const problem = problems.find(p => p._id === sub.problemId);
      return problem?.difficulty === 'Hard';
    }).length;

    return { total, completed, easy, medium, hard, completedEasy, completedMedium, completedHard };
  };

  const stats = getProgressStats();

  return (
    <div className="min-h-screen leetcode-bg-primary">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* LeetCode-Style Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-orange-500 rounded">
                <CodeBracketIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-semibold leetcode-text-primary">Problems</h1>
                <p className="leetcode-text-muted text-sm mt-1">
                  Solve problems and improve your skills
                </p>
              </div>
            </div>

            {session?.user?.role === 'admin' && (
              <button
                onClick={() => router.push('/coding/create-question')}
                className="inline-flex items-center px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 transition-colors font-medium text-sm leetcode-button-hover"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Problem
              </button>
            )}
          </div>
        </div>

        {/* LeetCode-Style Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="leetcode-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs leetcode-text-muted uppercase tracking-wide">Solved</p>
                <p className="text-xl font-semibold leetcode-text-primary">
                  {stats.completed}<span className="text-sm leetcode-text-muted">/{stats.total}</span>
                </p>
              </div>
              <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                <CheckCircleIcon className="h-5 w-5 text-white" />
              </div>
            </div>
            <div className="mt-3">
              <div className="w-full bg-gray-700 rounded-full h-1.5">
                <div
                  className="bg-green-500 h-1.5 rounded-full transition-all duration-300"
                  style={{ width: `${stats.total > 0 ? (stats.completed / stats.total) * 100 : 0}%` }}
                />
              </div>
            </div>
          </div>

          <div className="leetcode-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs leetcode-text-muted uppercase tracking-wide">Easy</p>
                <p className="text-xl font-semibold leetcode-easy">
                  {stats.completedEasy}<span className="text-sm leetcode-text-muted">/{stats.easy}</span>
                </p>
              </div>
              <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-full" />
              </div>
            </div>
          </div>

          <div className="leetcode-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs leetcode-text-muted uppercase tracking-wide">Medium</p>
                <p className="text-xl font-semibold leetcode-medium">
                  {stats.completedMedium}<span className="text-sm leetcode-text-muted">/{stats.medium}</span>
                </p>
              </div>
              <div className="w-8 h-8 bg-yellow-500 rounded flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-full" />
              </div>
            </div>
          </div>

          <div className="leetcode-card p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs leetcode-text-muted uppercase tracking-wide">Hard</p>
                <p className="text-xl font-semibold leetcode-hard">
                  {stats.completedHard}<span className="text-sm leetcode-text-muted">/{stats.hard}</span>
                </p>
              </div>
              <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-full" />
              </div>
            </div>
          </div>
        </div>

        {/* LeetCode-Style Search and Filters */}
        <div className="leetcode-card p-4 mb-6">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 leetcode-text-muted" />
              <input
                type="text"
                placeholder="Search questions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border leetcode-border rounded bg-transparent leetcode-text-primary placeholder-gray-500 focus:ring-1 focus:ring-orange-500 focus:border-orange-500 transition-colors"
              />
            </div>

            {/* Filter Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className={`inline-flex items-center px-3 py-1.5 rounded text-sm font-medium transition-colors ${
                  showFilters
                    ? 'leetcode-text-primary bg-gray-700'
                    : 'leetcode-text-muted hover:leetcode-text-primary hover:bg-gray-700'
                }`}
              >
                <AdjustmentsHorizontalIcon className="h-4 w-4 mr-1.5" />
                Filters
              </button>

              {(difficultyFilter || topicFilter) && (
                <button
                  onClick={clearFilters}
                  className="text-sm leetcode-text-muted hover:leetcode-text-primary"
                >
                  Clear
                </button>
              )}
            </div>
          </div>

          {/* Filter Options */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Difficulty
                  </label>
                  <select
                    value={difficultyFilter}
                    onChange={(e) => setDifficultyFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                  >
                    <option value="">All Difficulties</option>
                    <option value="Easy">Easy</option>
                    <option value="Medium">Medium</option>
                    <option value="Hard">Hard</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Topic
                  </label>
                  <select
                    value={topicFilter}
                    onChange={(e) => setTopicFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-brand-500 focus:border-transparent"
                  >
                    <option value="">All Topics</option>
                    {allTopics.map(topic => (
                      <option key={topic} value={topic}>{topic}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* LeetCode-Style Problems List */}
        {loading ? (
          <div className="flex justify-center py-12">
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-600 border-t-orange-500"></div>
          </div>
        ) : (
          <div className="leetcode-card overflow-hidden">
            {filteredProblems.length === 0 ? (
              <div className="text-center py-12">
                <CodeBracketIcon className="h-12 w-12 leetcode-text-muted mx-auto mb-4" />
                <h3 className="text-lg font-medium leetcode-text-primary mb-2">No problems found</h3>
                <p className="leetcode-text-muted">
                  {searchTerm || difficultyFilter || topicFilter
                    ? 'Try adjusting your search criteria'
                    : 'No problems available yet'}
                </p>
              </div>
            ) : (
              <>
                {/* LeetCode-Style Desktop Table */}
                <div className="hidden md:block overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b leetcode-border">
                          <th className="px-4 py-3 text-left text-xs font-medium leetcode-text-muted uppercase tracking-wider">
                            Status
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium leetcode-text-muted uppercase tracking-wider">
                            Title
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium leetcode-text-muted uppercase tracking-wider">
                            Difficulty
                          </th>
                          <th className="px-4 py-3 text-left text-xs font-medium leetcode-text-muted uppercase tracking-wider">
                            Topics
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y leetcode-border">
                        {filteredProblems.map((problem, index) => (
                          <tr
                            key={problem._id}
                            className="hover:bg-gray-800/50 transition-colors cursor-pointer"
                            onClick={() => router.push(`/coding/problem/${problem._id}`)}
                          >
                            <td className="px-4 py-3 whitespace-nowrap">
                              {isProblemCompleted(problem._id) ? (
                                <CheckCircleIcon className="h-4 w-4 text-green-500" />
                              ) : (
                                <div className="h-4 w-4 rounded-full border-2 border-gray-600" />
                              )}
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex items-center space-x-3">
                                <span className="text-sm leetcode-text-muted font-mono">
                                  {index + 1}.
                                </span>
                                <div>
                                  <div className="text-sm font-medium leetcode-text-primary hover:text-blue-400 transition-colors">
                                    {problem.title}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-4 py-3 whitespace-nowrap">
                              <span className={`text-sm font-medium ${
                                problem.difficulty === 'Easy' ? 'leetcode-easy' :
                                problem.difficulty === 'Medium' ? 'leetcode-medium' :
                                'leetcode-hard'
                              }`}>
                                {problem.difficulty}
                              </span>
                            </td>
                            <td className="px-4 py-3">
                              <div className="flex flex-wrap gap-1">
                                {problem.topics.slice(0, 2).map((topic, topicIndex) => (
                                  <span
                                    key={topicIndex}
                                    className="inline-flex rounded px-2 py-1 text-xs font-medium bg-gray-700 leetcode-text-muted"
                                  >
                                    {topic}
                                  </span>
                                ))}
                                {problem.topics.length > 2 && (
                                  <span className="inline-flex rounded px-2 py-1 text-xs font-medium bg-gray-700 leetcode-text-muted">
                                    +{problem.topics.length - 2}
                                  </span>
                                )}
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                </div>

                {/* LeetCode-Style Mobile Cards */}
                <div className="md:hidden">
                  <div className="space-y-3 p-4">
                    {filteredProblems.map((problem, index) => (
                      <div
                        key={problem._id}
                        className="leetcode-card p-4 cursor-pointer leetcode-hover"
                        onClick={() => router.push(`/coding/problem/${problem._id}`)}
                      >
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            {isProblemCompleted(problem._id) ? (
                              <CheckCircleIcon className="h-4 w-4 text-green-500 flex-shrink-0" />
                            ) : (
                              <div className="h-4 w-4 rounded-full border-2 border-gray-600 flex-shrink-0" />
                            )}
                            <div>
                              <div className="flex items-center space-x-2">
                                <span className="text-xs leetcode-text-muted font-mono">
                                  {index + 1}.
                                </span>
                                <h3 className="text-sm font-medium leetcode-text-primary">
                                  {problem.title}
                                </h3>
                              </div>
                              <span className={`inline-flex text-xs font-medium mt-1 ${
                                problem.difficulty === 'Easy' ? 'leetcode-easy' :
                                problem.difficulty === 'Medium' ? 'leetcode-medium' :
                                'leetcode-hard'
                              }`}>
                                {problem.difficulty}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-1">
                          {problem.topics.slice(0, 3).map((topic, topicIndex) => (
                            <span
                              key={topicIndex}
                              className="inline-flex rounded px-2 py-1 text-xs font-medium bg-gray-700 leetcode-text-muted"
                            >
                              {topic}
                            </span>
                          ))}
                          {problem.topics.length > 3 && (
                            <span className="inline-flex rounded px-2 py-1 text-xs font-medium bg-gray-700 leetcode-text-muted">
                              +{problem.topics.length - 3}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>
        )}

        {/* Results Summary */}
        {!loading && filteredProblems.length > 0 && (
          <div className="mt-6 text-center">
            <p className="text-sm leetcode-text-muted">
              Showing {filteredProblems.length} of {problems.length} problems
              {(searchTerm || difficultyFilter || topicFilter) && (
                <span className="ml-1">(filtered)</span>
              )}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}





