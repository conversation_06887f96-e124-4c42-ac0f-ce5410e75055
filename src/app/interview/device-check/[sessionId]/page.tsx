"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { VideoIcon } from '@/icons';
import { FaMicrophone, FaVideo, FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import Select from '@/components/form/Select';
import { use } from 'react';
import TestQuestionStore from '@/components/interview/TestQuestionStore';

export default function DeviceCheck({ params }: { params: { sessionId: string } | Promise<{ sessionId: string }> }) {
  const router = useRouter();
  // Use React's 'use' function to handle the params promise
  const { sessionId } = typeof params === 'object' && !('then' in params) ? params : use(params as Promise<{ sessionId: string }>);
  
  const [cameraReady, setCameraReady] = useState(false);
  const [micReady, setMicReady] = useState(false);
  const [cameraError, setCameraError] = useState('');
  const [micError, setMicError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // Device selection states
  const [videoDevices, setVideoDevices] = useState<MediaDeviceInfo[]>([]);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedVideoDevice, setSelectedVideoDevice] = useState<string>('');
  const [selectedAudioDevice, setSelectedAudioDevice] = useState<string>('');
  const [videoVisible, setVideoVisible] = useState(false);
  const [currentStream, setCurrentStream] = useState<MediaStream | null>(null);

  // Get available devices
  useEffect(() => {
    async function getDevices() {
      try {
        // Request permission first to get accurate device list
        await navigator.mediaDevices.getUserMedia({ video: true, audio: true })
          .then(stream => {
            // Stop the stream immediately after getting permissions
            stream.getTracks().forEach(track => track.stop());
          });
        
        const devices = await navigator.mediaDevices.enumerateDevices();
        
        const videos = devices.filter(device => device.kind === 'videoinput');
        const audios = devices.filter(device => device.kind === 'audioinput');
        
        setVideoDevices(videos);
        setAudioDevices(audios);
        
        // Set default selections if devices exist
        if (videos.length > 0) setSelectedVideoDevice(videos[0].deviceId);
        if (audios.length > 0) setSelectedAudioDevice(audios[0].deviceId);
        
        setIsLoading(false);
        
        // Call checkDevices after a short delay to ensure state is updated
        setTimeout(() => {
          checkDevices();
        }, 300);
      } catch (err) {
        console.error('Error getting devices:', err);
        setIsLoading(false);
      }
    }
    
    getDevices();
  }, []);

  // Check devices when selection changes
  useEffect(() => {
    if (selectedVideoDevice || selectedAudioDevice) {
      checkDevices();
    }
  }, [selectedVideoDevice, selectedAudioDevice]);

  // Add cleanup on component unmount
  useEffect(() => {
    return () => {
      // Clean up media streams when component unmounts
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
      }
    };
  }, [currentStream]);

  async function checkDevices() {
    try {
      setIsLoading(true);
      setCameraError('');
      setMicError('');
      
      // Stop any existing streams
      if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        setCurrentStream(null);
      }
      
      if (videoRef.current && videoRef.current.srcObject) {
        videoRef.current.srcObject = null;
      }
      
      // Set constraints based on selected devices
      const constraints: MediaStreamConstraints = {
        video: selectedVideoDevice ? { deviceId: { exact: selectedVideoDevice } } : true,
        audio: selectedAudioDevice ? { deviceId: { exact: selectedAudioDevice } } : true
      };
      
      console.log("Getting media with constraints:", constraints);
      
      // Get user media with a timeout
      const stream = await Promise.race([
        navigator.mediaDevices.getUserMedia(constraints),
        new Promise<MediaStream>((_, reject) => {
          setTimeout(() => reject(new Error("Device connection timeout")), 10000);
        })
      ]) as MediaStream;
      
      // Save the stream for later cleanup
      setCurrentStream(stream);
      
      // Check camera
      if (stream.getVideoTracks().length > 0) {
        const videoTrack = stream.getVideoTracks()[0];
        console.log("Video track:", videoTrack.label, "enabled:", videoTrack.enabled);
        
        // Ensure video track is enabled
        videoTrack.enabled = true;
        
        // Set the stream to video element
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
          
          // Play the video immediately
          try {
            await videoRef.current.play();
            console.log("Video playing successfully");
            setCameraReady(true);
            setCameraError('');
            setVideoVisible(true);
          } catch (e) {
            console.error("Error playing video:", e);
            setCameraError(`Error displaying video: ${e instanceof Error ? e.message : 'Unknown error'}`);
            setCameraReady(false);
            setVideoVisible(false);
          }
        } else {
          console.error("Video ref is null");
          setCameraError('Video element not found');
          setCameraReady(false);
          setVideoVisible(false);
        }
      } else {
        setCameraReady(false);
        setCameraError('No video track found in stream');
        setVideoVisible(false);
      }
      
      // Check microphone
      if (stream.getAudioTracks().length > 0) {
        setMicReady(true);
        setMicError('');
      } else {
        setMicReady(false);
        setMicError('No audio track found in stream');
      }
      
      setIsLoading(false);
    } catch (err) {
      console.error("Device check error:", err);
      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          setCameraError('Permission denied. Please allow camera and microphone access.');
          setMicError('Permission denied. Please allow camera and microphone access.');
        } else if (err.name === 'NotFoundError') {
          setCameraError('Camera not found. Please connect a camera.');
          setMicError('Microphone not found. Please connect a microphone.');
        } else {
          setCameraError(`Camera error: ${err.message}`);
          setMicError(`Microphone error: ${err.message}`);
        }
      }
      setCameraReady(false);
      setMicReady(false);
      setVideoVisible(false);
      setIsLoading(false);
    }
  }

  const handleVideoDeviceChange = (deviceId: string) => {
    // Only trigger device check if the selection actually changed
    if (deviceId !== selectedVideoDevice) {
      setSelectedVideoDevice(deviceId);
      // Add a small delay before reconnecting to ensure state is updated
      setTimeout(() => {
        checkDevices();
      }, 100);
    }
  };

  const handleAudioDeviceChange = (deviceId: string) => {
    setSelectedAudioDevice(deviceId);
    // Add a small delay before reconnecting to ensure state is updated
    setTimeout(() => {
      checkDevices();
    }, 100);
  };

  const handleContinue = () => {
    router.push(`/interview/session/${sessionId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="max-w-3xl w-full bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden">
        <div className="p-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white text-center mb-2">
            Device Check
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-center mb-8">
            Let's make sure your camera and microphone are working properly before starting the interview.
          </p>
          
          <div className="grid md:grid-cols-2 gap-8 mb-8">
            {/* Camera Preview */}
            <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-6 flex flex-col items-center">
              <div className="w-full mb-4">
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Select Camera
                </label>
                <Select
                  options={videoDevices.map(device => ({
                    value: device.deviceId,
                    label: device.label || `Camera ${videoDevices.indexOf(device) + 1}`
                  }))}
                  placeholder="Select camera"
                  onChange={handleVideoDeviceChange}
                  defaultValue={selectedVideoDevice}
                  className="mb-4"
                />
              </div>
              
              <div className="relative w-full aspect-video bg-black rounded-lg overflow-hidden mb-4">
                {isLoading ? (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
                  </div>
                ) : (
                  <>
                    <video 
                      ref={videoRef} 
                      autoPlay 
                      playsInline 
                      muted 
                      className={`w-full h-full object-cover ${videoVisible ? 'block' : 'hidden'}`}
                    />
                    {(!videoVisible || !cameraReady) && !isLoading && (
                      <div className="absolute inset-0 flex flex-col items-center justify-center text-red-500">
                        <FaTimesCircle size={48} className="mb-2" />
                        <p className="text-sm text-center px-4">{cameraError || 'Video element not found'}</p>
                      </div>
                    )}
                  </>
                )}
              </div>
              
              <div className="flex items-center space-x-3 w-full">
                <FaVideo className={`text-2xl ${cameraReady ? 'text-green-500' : 'text-red-500'}`} />
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Camera</h3>
                  {cameraError ? (
                    <p className="text-sm text-red-500">{cameraError}</p>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {cameraReady ? 'Camera is working properly' : 'Video element not found'}
                    </p>
                  )}
                </div>
                {cameraReady && <FaCheckCircle className="text-green-500 ml-auto" />}
              </div>
              {/* Removed the reconnect button */}
            </div>
            
            {/* Microphone Check */}
            <div className="bg-gray-100 dark:bg-gray-700 rounded-xl p-6 flex flex-col">
              <div className="w-full mb-4">
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Select Microphone
                </label>
                <Select
                  options={audioDevices.map(device => ({
                    value: device.deviceId,
                    label: device.label || `Microphone ${audioDevices.indexOf(device) + 1}`
                  }))}
                  placeholder="Select microphone"
                  onChange={handleAudioDeviceChange}
                  defaultValue={selectedAudioDevice}
                  className="mb-4"
                />
              </div>
              
              <div className="flex-1 flex flex-col items-center justify-center mb-4">
                <div className={`w-32 h-32 rounded-full flex items-center justify-center ${
                  micReady ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30'
                }`}>
                  <FaMicrophone className={`text-5xl ${
                    micReady ? 'text-green-500' : 'text-red-500'
                  }`} />
                </div>
                
                {micReady && (
                  <div className="mt-4 flex space-x-1">
                    <div className="w-1 h-8 bg-green-500 rounded-full animate-pulse"></div>
                    <div className="w-1 h-12 bg-green-500 rounded-full animate-pulse delay-75"></div>
                    <div className="w-1 h-6 bg-green-500 rounded-full animate-pulse delay-100"></div>
                    <div className="w-1 h-10 bg-green-500 rounded-full animate-pulse delay-150"></div>
                    <div className="w-1 h-4 bg-green-500 rounded-full animate-pulse delay-200"></div>
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-3 w-full">
                <FaMicrophone className={`text-2xl ${micReady ? 'text-green-500' : 'text-red-500'}`} />
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">Microphone</h3>
                  {micError ? (
                    <p className="text-sm text-red-500">{micError}</p>
                  ) : (
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {micReady ? 'Microphone is working properly' : 'Checking microphone...'}
                    </p>
                  )}
                </div>
                {micReady && <FaCheckCircle className="text-green-500 ml-auto" />}
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => router.back()}
              className="px-6 py-3 rounded-lg border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Go Back
            </button>
            <button
              onClick={handleContinue}
              disabled={isLoading || !cameraReady || !micReady}
              className="px-6 py-3 rounded-lg bg-indigo-600 text-white font-medium hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-indigo-500/20"
            >
              {isLoading ? 'Checking Devices...' : 'Continue to Interview'}
            </button>
          </div>
        </div>
      </div>
      <div className="mt-8">
        {/* TestQuestionStore component removed */}
      </div>
    </div>
  );
}
