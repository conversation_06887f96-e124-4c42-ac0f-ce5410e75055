'use client';

import { useState, useEffect, useRef, use } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { 
  loadResultsFromIndexedDB, 
  getSessionResults, 
  getSessionQueueStats,
  getProcessingResult
} from '@/services/videoProcessingService';
import { <PERSON>a<PERSON><PERSON><PERSON>, FaCheckCircle, FaExclamationTriangle, FaHourglassHalf, FaDownload, FaChartBar, FaUser, FaCalendarAlt } from 'react-icons/fa';

export default function InterviewResults({ params }: { params: { sessionId: string } | Promise<{ sessionId: string }> }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [responses, setResponses] = useState<any[]>([]);
  const [sessionData, setSessionData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [questions, setQuestions] = useState<any[]>([]);
  const [queueStats, setQueueStats] = useState<any>({ total: 0, pending: 0, processing: 0, completed: 0, error: 0 });
  const resultsRef = useRef<HTMLDivElement>(null);
  
  // Properly unwrap the params using React.use() if it's a Promise
  const { sessionId } = typeof params === 'object' && !('then' in params) 
    ? params 
    : use(params as Promise<{ sessionId: string }>);

  // Function to refresh results
  const refreshResults = async () => {
    try {
      // Get queue stats
      const stats = getSessionQueueStats(sessionId);
      setQueueStats(stats);
      
      // Load results from IndexedDB
      const results = await loadResultsFromIndexedDB(sessionId);
      
      // Also get any in-memory results not yet saved to IndexedDB
      const memoryResults = getSessionResults(sessionId);
      
      // Merge results, preferring IndexedDB versions
      const mergedResults = [...memoryResults];
      
      // Update with IndexedDB results
      for (const dbResult of results) {
        const existingIndex = mergedResults.findIndex(r => r.questionId === dbResult.questionId);
        if (existingIndex >= 0) {
          mergedResults[existingIndex] = dbResult;
        } else {
          mergedResults.push(dbResult);
        }
      }
      
      setResponses(mergedResults);
      
      // Check if we need to continue polling
      const allComplete = stats.pending === 0 && stats.processing === 0;
      return allComplete;
    } catch (error) {
      console.error('Error refreshing results:', error);
      return false;
    }
  };
  
  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true);
        
        // Load session data
        try {
          const sessionResponse = await fetch(`/api/sessions/${sessionId}`);
          if (sessionResponse.ok) {
            const sessionData = await sessionResponse.json();
            setSessionData(sessionData);
            // Also save to localStorage as backup
            localStorage.setItem(`session-${sessionId}`, JSON.stringify(sessionData));
          } else {
            // Try to get from localStorage
            const localData = localStorage.getItem(`session-${sessionId}`);
            if (localData) {
              setSessionData(JSON.parse(localData));
            }
          }
        } catch (error) {
          console.error('Error fetching session:', error);
          // Try one more time from localStorage
          const localData = localStorage.getItem(`session-${sessionId}`);
          if (localData) {
            setSessionData(JSON.parse(localData));
          }
        }
        
        // Load questions
        try {
          const questionsResponse = await fetch(`/api/sessions/${sessionId}/questions`);
          if (questionsResponse.ok) {
            const questionsData = await questionsResponse.json();
            setQuestions(questionsData);
          }
        } catch (error) {
          console.error('Error fetching questions:', error);
          // Continue anyway
        }
        
        // Initial refresh of results
        await refreshResults();
        
        // Set up polling for updates
        const pollInterval = setInterval(async () => {
          const allComplete = await refreshResults();
          if (allComplete) {
            clearInterval(pollInterval);
          }
        }, 2000);
        
        // Clean up interval on unmount
        return () => clearInterval(pollInterval);
      } catch (error) {
        console.error('Error fetching data:', error);
        setError(error instanceof Error ? error.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    }
    
    if (session?.user?.email) {
      fetchData();
    }
  }, [sessionId, session?.user?.email]);
  
  const downloadPDF = async () => {
    if (!resultsRef.current) return;
    
    try {
      // Create PDF document
      const pdf = new jsPDF('p', 'mm', 'a4');
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      
      // First, render the content to a canvas
      console.log('Creating PDF: Capturing content...');
      const canvas = await html2canvas(resultsRef.current, {
        scale: 2, // Higher scale for better quality
        useCORS: true, // Enable CORS for images
        logging: false,
        allowTaint: true,
        backgroundColor: '#1f2937' // Match the background color
      });
      
      // Add header with gradient background
      pdf.setFillColor(41, 65, 171); // Indigo color
      pdf.rect(0, 0, pdfWidth, 40, 'F');
      
      // Add title
      pdf.setTextColor(255, 255, 255);
      pdf.setFontSize(24);
      pdf.setFont(undefined, 'bold');
      pdf.text('MOCKLY', 15, 20);
      pdf.setFontSize(16);
      pdf.setFont(undefined, 'normal');
      pdf.text('Interview Assessment Report', 15, 30);
      
      // Add date and candidate info
      pdf.setTextColor(80, 80, 80);
      pdf.setFontSize(12);
      pdf.text(`Date: ${new Date().toLocaleDateString()}`, pdfWidth - 15, 50, { align: 'right' });
      pdf.text(`Candidate: ${session?.user?.name || 'Anonymous'}`, 15, 50);
      pdf.text(`Email: ${session?.user?.email || 'N/A'}`, 15, 58);
      
      // Add session info section
      pdf.setDrawColor(200, 200, 200);
      pdf.setLineWidth(0.5);
      pdf.line(15, 65, pdfWidth - 15, 65);
      
      pdf.setTextColor(41, 65, 171);
      pdf.setFontSize(16);
      pdf.setFont(undefined, 'bold');
      pdf.text('Session Information', 15, 75);
      
      pdf.setTextColor(80, 80, 80);
      pdf.setFontSize(12);
      pdf.setFont(undefined, 'normal');
      pdf.text(`Role: ${sessionData?.role || 'N/A'}`, 15, 85);
      pdf.text(`Experience Level: ${sessionData?.experienceLevel || 'N/A'}`, 15, 93);
      
      // Add responses section
      pdf.setDrawColor(200, 200, 200);
      pdf.line(15, 100, pdfWidth - 15, 100);
      
      pdf.setTextColor(41, 65, 171);
      pdf.setFontSize(16);
      pdf.setFont(undefined, 'bold');
      pdf.text('Interview Responses', 15, 110);
      
      // Add each response
      let yPosition = 125;
      
      for (let i = 0; i < responses.length; i++) {
        const response = responses[i];
        
        // Check if we need a new page
        if (yPosition > pdfHeight - 40) {
          pdf.addPage();
          yPosition = 20;
        }
        
        // Question
        pdf.setTextColor(41, 65, 171);
        pdf.setFontSize(14);
        pdf.setFont(undefined, 'bold');
        pdf.text(`Question ${i + 1}:`, 15, yPosition);
        yPosition += 8;
        
        // Question text (with word wrap)
        pdf.setTextColor(80, 80, 80);
        pdf.setFontSize(12);
        pdf.setFont(undefined, 'normal');
        
        const questionText = response.question || 'Question not available';
        const questionLines = pdf.splitTextToSize(questionText, pdfWidth - 30);
        pdf.text(questionLines, 15, yPosition);
        yPosition += questionLines.length * 7 + 5;
        
        // Answer
        pdf.setTextColor(80, 80, 80);
        pdf.setFontSize(12);
        pdf.setFont(undefined, 'italic');
        pdf.text('Your Answer:', 15, yPosition);
        yPosition += 7;
        
        pdf.setFont(undefined, 'normal');
        const answerText = response.answer || 'No answer recorded';
        const answerLines = pdf.splitTextToSize(answerText, pdfWidth - 30);
        pdf.text(answerLines, 15, yPosition);
        yPosition += answerLines.length * 7 + 5;
        
        // Confidence Score
        if (response.responseAnalysis?.overall_assessment?.overall_score) {
          const score = response.responseAnalysis.overall_assessment.overall_score;
          pdf.setTextColor(41, 65, 171);
          pdf.setFontSize(12);
          pdf.setFont(undefined, 'bold');
          pdf.text('Confidence Score:', 15, yPosition);
          
          // Draw score bar
          const barWidth = 100;
          pdf.setDrawColor(220, 220, 220);
          pdf.setFillColor(220, 220, 220);
          pdf.roundedRect(50, yPosition - 3, barWidth, 5, 2, 2, 'F');
          
          // Calculate score width (score is 1-10)
          const scoreWidth = (score / 10) * barWidth;
          
          // Set color based on score
          if (score >= 7) {
            pdf.setFillColor(39, 174, 96); // Green
          } else if (score >= 4) {
            pdf.setFillColor(241, 196, 15); // Yellow
          } else {
            pdf.setFillColor(231, 76, 60); // Red
          }
          
          pdf.roundedRect(50, yPosition - 3, scoreWidth, 5, 2, 2, 'F');
          
          // Add score text
          pdf.setTextColor(80, 80, 80);
          pdf.setFontSize(10);
          pdf.text(`${score}/10`, 155, yPosition);
          
          yPosition += 15;
        }
        
        // Feedback (if available)
        if (response.feedback) {
          // Check if we need a new page
          if (yPosition > pdfHeight - 60) {
            pdf.addPage();
            yPosition = 20;
          }
          
          pdf.setTextColor(41, 65, 171);
          pdf.setFontSize(12);
          pdf.setFont(undefined, 'bold');
          pdf.text('AI Feedback:', 15, yPosition);
          yPosition += 7;
          
          pdf.setTextColor(80, 80, 80);
          pdf.setFont(undefined, 'normal');
          const feedbackLines = pdf.splitTextToSize(response.feedback, pdfWidth - 30);
          pdf.text(feedbackLines, 15, yPosition);
          yPosition += feedbackLines.length * 7 + 15;
        } else {
          yPosition += 15;
        }
        
        // Add separator line between questions
        if (i < responses.length - 1) {
          pdf.setDrawColor(220, 220, 220);
          pdf.setLineWidth(0.3);
          pdf.line(15, yPosition - 10, pdfWidth - 15, yPosition - 10);
        }
      }
      
      // Add footer on each page
      const pageCount = pdf.getNumberOfPages();
      for (let i = 1; i <= pageCount; i++) {
        pdf.setPage(i);
        
        // Footer line
        pdf.setDrawColor(200, 200, 200);
        pdf.setLineWidth(0.5);
        pdf.line(15, pdfHeight - 20, pdfWidth - 15, pdfHeight - 20);
        
        // Footer text
        pdf.setTextColor(150, 150, 150);
        pdf.setFontSize(10);
        pdf.text('Powered by Mockly - AI-Powered Interview Platform', pdfWidth / 2, pdfHeight - 10, { align: 'center' });
        pdf.text(`Page ${i} of ${pageCount}`, pdfWidth - 15, pdfHeight - 10, { align: 'right' });
      }
      
      console.log('PDF generated, saving...');
      pdf.save(`mockly-interview-results-${sessionId}-${new Date().toISOString().slice(0, 10)}.pdf`);
      console.log('PDF saved successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('There was an error generating the PDF. Please try again.');
    }
  };
  
  // For the loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500"></div>
      </div>
    );
  }

  // For the error state
  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-black">
        <div className="bg-gray-800 p-6 rounded-xl shadow-lg max-w-md">
          <h2 className="text-xl font-bold text-red-600 mb-4">Error</h2>
          <p className="text-gray-300">{error}</p>
          <button 
            onClick={() => router.push('/interview')}
            className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-gray-800 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-white flex items-center">
            <FaChartBar className="mr-3 text-indigo-400" /> 
            Interview Results
          </h1>
          <button
            onClick={downloadPDF}
            className="px-5 py-3 bg-indigo-600 text-white rounded-lg font-medium hover:bg-indigo-700 transition-colors flex items-center shadow-lg"
          >
            <FaDownload className="mr-2" /> Download PDF
          </button>
        </div>
        
        <div ref={resultsRef} className="bg-gray-800 rounded-xl shadow-2xl p-8 mb-8 w-full border border-gray-700">
          {/* Session Info */}
          <div className="mb-8 pb-6 border-b border-gray-700">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <FaUser className="mr-3 text-indigo-400" />
              Session Information
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-700/50 p-5 rounded-lg shadow-inner">
                <p className="text-sm text-gray-400 mb-2">Role</p>
                <p className="text-xl font-medium text-white">{sessionData?.role || 'N/A'}</p>
              </div>
              <div className="bg-gray-700/50 p-5 rounded-lg shadow-inner">
                <p className="text-sm text-gray-400 mb-2">Experience Level</p>
                <p className="text-xl font-medium text-white">{sessionData?.experienceLevel || 'N/A'}</p>
              </div>
              <div className="bg-gray-700/50 p-5 rounded-lg shadow-inner">
                <p className="text-sm text-gray-400 mb-2">Date</p>
                <p className="text-xl font-medium text-white flex items-center">
                  <FaCalendarAlt className="mr-2 text-indigo-400" />
                  {new Date().toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
          
          {/* Responses */}
          <div className="w-full">
            <h2 className="text-2xl font-semibold text-white mb-6 flex items-center">
              <FaChartBar className="mr-3 text-indigo-400" />
              Interview Responses
            </h2>
            {responses.length > 0 ? (
              responses.map((response, index) => (
                <div key={index} className="mb-8 pb-6 border-b border-gray-700 last:border-0 w-full">
                  <div className="bg-gray-700/30 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                    <h3 className="text-xl font-medium text-white mb-4 border-l-4 border-indigo-500 pl-4">
                      Question {index + 1}: {response.question}
                    </h3>
                    <div className="bg-gray-800/50 p-5 rounded-lg mb-6">
                      <h4 className="text-md font-medium text-gray-300 mb-2">Your Answer:</h4>
                      <p className="text-gray-300 leading-relaxed">{response.answer}</p>
                    </div>
                    
                    {/* Confidence Score */}
                    {response.responseAnalysis?.overall_assessment?.overall_score && (
                      <div className="bg-gray-800/50 p-5 rounded-lg mb-6">
                        <h4 className="text-md font-medium text-gray-300 mb-2">Confidence Score:</h4>
                        <div className="flex items-center">
                          <div className="w-full bg-gray-700 rounded-full h-2.5 mr-2">
                            <div 
                              className={`h-2.5 rounded-full ${
                                response.responseAnalysis.overall_assessment.overall_score >= 7 
                                  ? 'bg-green-500' 
                                  : response.responseAnalysis.overall_assessment.overall_score >= 4 
                                    ? 'bg-yellow-500' 
                                    : 'bg-red-500'
                              }`} 
                              style={{ width: `${response.responseAnalysis.overall_assessment.overall_score * 10}%` }}
                            ></div>
                          </div>
                          <span className="text-white font-medium">
                            {response.responseAnalysis.overall_assessment.overall_score}/10
                          </span>
                        </div>
                      </div>
                    )}
                    
                    {response.feedback && (
                      <div className="bg-indigo-900/30 p-5 rounded-lg border-l-4 border-indigo-500">
                        <h4 className="text-md font-medium text-white mb-2 flex items-center">
                          <FaCheckCircle className="mr-2 text-indigo-400" /> AI Feedback
                        </h4>
                        <p className="text-gray-300 leading-relaxed">{response.feedback}</p>
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="bg-gray-700/30 p-8 rounded-xl text-center">
                <FaExclamationTriangle className="text-4xl text-yellow-500 mx-auto mb-4" />
                <p className="text-gray-400 text-lg">No responses found for this session.</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
