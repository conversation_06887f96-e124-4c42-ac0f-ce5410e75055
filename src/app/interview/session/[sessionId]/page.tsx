'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { FaVideo, FaMicrophone, FaStopCircle, FaArrowRight } from 'react-icons/fa';
import { processVideoRecording, getSessionResults, getProcessingResult } from '@/services/videoProcessingService';

export default function InterviewSession({ params }: { params: { sessionId: string } }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [questions, setQuestions] = useState<any[]>([]);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isFinished, setIsFinished] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [sessionId, setSessionId] = useState('');
  const [hasRecordedAny, setHasRecordedAny] = useState(false);
  const [processingTasks, setProcessingTasks] = useState<string[]>([]);
  
  // Store sessionId in state to avoid the Next.js params warning
  useEffect(() => {
    if (params.sessionId) {
      setSessionId(params.sessionId);
    }
  }, [params]);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const chunksRef = useRef<Blob[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Add this function to check supported MIME types
  const getSupportedMimeType = () => {
    const types = [
      'video/webm;codecs=vp9,opus',
      'video/webm;codecs=vp8,opus',
      'video/webm',
      'video/mp4'
    ];
    
    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }
    
    return 'video/webm'; // Default fallback
  };

  // Fetch questions for this session
  useEffect(() => {
    async function fetchQuestions() {
      if (!sessionId) return;
      
      try {
        setIsLoading(true);
        const response = await fetch(`/api/sessions/${sessionId}/questions`);
        
        if (response.ok) {
          const data = await response.json();
          console.log("Fetched questions:", data);
          setQuestions(data);
        } else {
          console.error('Failed to fetch questions:', await response.text());
        }
      } catch (error) {
        console.error('Error fetching questions:', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchQuestions();
  }, [sessionId]);

  // Initialize camera
  useEffect(() => {
    async function setupCamera() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ 
          video: true, 
          audio: true 
        });
        
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
        
        streamRef.current = stream;
      } catch (error) {
        console.error('Error accessing camera:', error);
      }
    }
    
    setupCamera();
    
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  const startRecording = () => {
    if (!streamRef.current) return;
    
    chunksRef.current = [];
    
    // Get supported MIME type
    const mimeType = getSupportedMimeType();
    console.log('Using MIME type:', mimeType);
    
    try {
      const mediaRecorder = new MediaRecorder(streamRef.current, {
        mimeType: mimeType
      });
      
      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };
      
      mediaRecorder.onstop = processRecording;
      
      mediaRecorder.start();
      mediaRecorderRef.current = mediaRecorder;
      setIsRecording(true);
      setRecordingTime(0);
      
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error starting recording:', error);
      alert('Failed to start recording. Please try again or use a different browser.');
    }
  };
  
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      setIsProcessing(true);
      
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }
  };
  
  const processRecording = async () => {
    if (chunksRef.current.length === 0) {
      setIsProcessing(false);
      return;
    }
    
    setHasRecordedAny(true);
    setIsProcessing(true);
    
    try {
      const currentQuestion = questions[currentQuestionIndex];
      
      // Create a proper video blob with the correct MIME type
      const videoBlob = new Blob(chunksRef.current, { 
        type: mediaRecorderRef.current?.mimeType || 'video/webm' 
      });
      
      console.log('Video blob created:', videoBlob.size, 'bytes, type:', videoBlob.type);
      
      // Add to processing queue and get task ID
      const taskId = await processVideoRecording(
        params.sessionId,
        currentQuestion?._id || `question-${currentQuestionIndex}`,
        currentQuestion?.text || "Question text not available",
        videoBlob
      );
      
      console.log('Added to processing queue with task ID:', taskId);
      
      // Add task to our tracking array
      setProcessingTasks(prev => [...prev, taskId]);
      
      // Move to next question or finish
      if (currentQuestionIndex < questions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
      } else {
        setIsFinished(true);
      }
    } catch (error) {
      console.error('Error processing response:', error);
      alert('There was an error processing your recording. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };
  
  const handleNextQuestion = () => {
    if (isRecording) {
      stopRecording();
    } else if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    } else {
      // Before navigating to results, ensure we have all the processed data
      const results = getSessionResults(params.sessionId);
      
      // Navigate to results page with the session ID
      router.push(`/interview/results/${params.sessionId}`);
    }
  };
  
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  if (isFinished) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-black p-4">
        <div className="bg-gray-800 rounded-xl shadow-2xl p-8 max-w-lg w-full text-center">
          <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold mb-4 text-white">Interview Complete!</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-8">
            You've completed all the questions. Your responses have been recorded and analyzed.
          </p>
          <button
            onClick={() => router.push(`/interview/results/${params.sessionId}`)}
            className="w-full px-6 py-3 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium hover:from-indigo-700 hover:to-blue-700 transition-all shadow-lg"
          >
            View Results
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-black p-4 flex flex-col">
      <div className="max-w-6xl mx-auto w-full flex-grow grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left side - Video and questions */}
        <div className="lg:col-span-2 space-y-6 flex flex-col">
          {/* Question display */}
          <div className="bg-gray-800 rounded-xl shadow-2xl p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium text-gray-200">
                Question {currentQuestionIndex + 1} of {questions.length}
              </h2>
              <div className="text-sm font-medium px-4 py-2 rounded-full bg-gray-700 text-gray-300">
                {isRecording ? (
                  <span className="flex items-center">
                    <span className="h-2 w-2 bg-red-500 rounded-full mr-2 animate-pulse"></span>
                    Recording: {formatTime(recordingTime)}
                  </span>
                ) : (
                  <span>Not recording</span>
                )}
              </div>
            </div>
            
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-10">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-500 mb-4"></div>
                <p className="text-gray-500">Loading question...</p>
              </div>
            ) : questions && questions.length > 0 ? (
              <div className="text-xl font-medium text-gray-900 dark:text-white mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-indigo-500">
                {questions[currentQuestionIndex]?.text || "Question text not available"}
              </div>
            ) : (
              <div className="text-xl font-medium text-gray-900 dark:text-white mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border-l-4 border-red-500">
                No questions available. Please check your session configuration.
              </div>
            )}
          </div>
          
          {/* Video display */}
          <div className="bg-black rounded-xl overflow-hidden aspect-video shadow-2xl flex-grow">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
          </div>
          
          {/* Controls */}
          <div className="flex gap-4 justify-center">
            {isProcessing ? (
              <div className="w-full py-4 bg-gray-100 dark:bg-gray-800 rounded-lg flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-500 mr-3"></div>
                <span className="text-gray-700 dark:text-gray-300">Processing your response...</span>
              </div>
            ) : isRecording ? (
              <button
                onClick={stopRecording}
                className="flex-1 px-6 py-4 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors flex items-center justify-center gap-2 shadow-lg"
              >
                <FaStopCircle className="text-xl" />
                Stop Recording
              </button>
            ) : (
              <>
                {/* First button: Start Recording/Interview */}
                <button
                  onClick={startRecording}
                  className="flex-1 px-6 py-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-lg font-medium hover:from-green-600 hover:to-emerald-700 transition-colors flex items-center justify-center gap-2 shadow-lg"
                >
                  <FaMicrophone className="text-xl" />
                  {currentQuestionIndex === 0 && !hasRecordedAny ? "Start Interview" : "Start Recording"}
                </button>
                
                {/* Second button: Next Question or Finish Interview */}
                {!isRecording && !isProcessing && (
                  <button
                    onClick={handleNextQuestion}
                    className="flex-1 px-6 py-4 bg-gradient-to-r from-indigo-600 to-blue-600 text-white rounded-lg font-medium hover:from-indigo-700 hover:to-blue-700 transition-colors flex items-center justify-center gap-2 shadow-lg"
                  >
                    {currentQuestionIndex < questions.length - 1 ? (
                      <>Next Question <FaArrowRight /></>
                    ) : (
                      <>Finish Interview</>
                    )}
                  </button>
                )}
              </>
            )}
          </div>
        </div>
        
        {/* Right side - Real-time analysis */}
        <div className="bg-gray-800 rounded-xl shadow-2xl p-6 h-full flex flex-col">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Real-time Analysis</h2>
          
          {/* Confidence Score */}
          <div className="mb-6">
            <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Confidence Score</h3>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
              <div className="bg-green-500 h-4 rounded-full" style={{ width: '75%' }}></div>
            </div>
            <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
              <span>Low</span>
              <span>Medium</span>
              <span>High</span>
            </div>
          </div>
          
          {/* Emotion Detection */}
          <div className="mb-6">
            <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Detected Emotions</h3>
            <div className="grid grid-cols-2 gap-2">
              <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Neutral</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">68%</span>
                </div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Confident</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">22%</span>
                </div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Thoughtful</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">8%</span>
                </div>
              </div>
              <div className="bg-gray-100 dark:bg-gray-700 p-2 rounded-lg">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Nervous</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">2%</span>
                </div>
              </div>
            </div>
          </div>
          
          {/* Speech Analysis */}
          <div className="mb-6 flex-grow">
            <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Speech Analysis</h3>
            <div className="space-y-3">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Pace</span>
                  <span className="text-xs text-green-600 dark:text-green-400">Good</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '70%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Clarity</span>
                  <span className="text-xs text-indigo-600 dark:text-indigo-400">Excellent</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-indigo-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                </div>
              </div>
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-700 dark:text-gray-300">Filler Words</span>
                  <span className="text-xs text-yellow-600 dark:text-yellow-400">Moderate</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '50%' }}></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Tips */}
          <div>
            <h3 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-2">Live Tips</h3>
            <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-green-500">•</span>
                Good eye contact, maintain this level
              </li>
              <li className="flex items-start gap-2">
                <span className="text-yellow-500">•</span>
                Try to reduce filler words like "um" and "uh"
              </li>
              <li className="flex items-start gap-2">
                <span className="text-indigo-500">•</span>
                Consider adding more specific examples
              </li>
            </ul>
          </div>
        </div>
      </div>
      {/* Enhanced debug info */}
      {processingTasks.length > 0 && (
        <div className="mt-4 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs">
          <div className="font-semibold mb-1">Processing Status:</div>
          {processingTasks.map(taskId => {
            const result = getProcessingResult(taskId);
            return (
              <div key={taskId} className="flex flex-col mb-2">
                <div className="flex justify-between">
                  <span>Question {taskId.split('-').pop()}</span>
                  <span className={
                    result?.status === 'completed' ? 'text-green-500' : 
                    result?.status === 'failed' ? 'text-red-500' : 
                    result?.status === 'processing' ? 'text-blue-500' : 'text-gray-500'
                  }>
                    {result?.status || 'pending'}
                  </span>
                </div>
                {result?.error && (
                  <div className="text-red-500 mt-1 text-xs">
                    Error: {result.error}
                  </div>
                )}
                {result?.transcript && (
                  <div className="text-gray-500 mt-1 text-xs">
                    Transcript: {result.transcript.substring(0, 50)}...
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
