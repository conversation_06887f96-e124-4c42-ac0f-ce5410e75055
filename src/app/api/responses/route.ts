import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import InterviewResponse from "@/models/interviewResponse";

export async function POST(req: NextRequest) {
  try {
    await connectToDatabase();
    
    const data = await req.json();
    
    // Validate required fields
    if (!data.userId || !data.sessionId || !data.questionId || !data.questionText) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }
    
    // Create new response
    const newResponse = new InterviewResponse(data);
    await newResponse.save();
    
    return NextResponse.json(
      { message: "Response saved successfully", id: newResponse._id },
      { status: 201 }
    );
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { message: "Failed to save response", error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    await connectToDatabase();
    
    const url = new URL(req.url);
    const sessionId = url.searchParams.get("sessionId");
    const userId = url.searchParams.get("userId");
    
    if (!sessionId || !userId) {
      return NextResponse.json(
        { message: "Missing required parameters" },
        { status: 400 }
      );
    }
    
    const responses = await InterviewResponse.find({
      sessionId,
      userId
    }).sort({ createdAt: 1 }).lean();
    
    return NextResponse.json(responses);
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { message: "Failed to fetch responses", error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}