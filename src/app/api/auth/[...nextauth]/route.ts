import NextAuth, { NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import GitHubProvider from "next-auth/providers/github";
import CredentialsProvider from "next-auth/providers/credentials";
import { connectToDatabase } from "@/lib/db";
import User from "@/models/user";
import { verifyPassword } from "@/lib/auth";

export const authOptions: NextAuthOptions = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_ID!,
      clientSecret: process.env.GITHUB_SECRET!,
    }),
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          await connectToDatabase();
          
          const user = await User.findOne({ email: credentials.email }).exec();

          if (!user) {
            return null;
          }

          if (!user.password) {
            throw new Error("Please use the appropriate sign-in method for this account");
          }

          const isValid = await verifyPassword(
            credentials.password,
            user.password
          );

          if (!isValid) {
            return null;
          }

          return {
            id: user._id.toString(),
            email: user.email,
            name: user.name,
            image: user.image,
          };
        } catch (error) {
          console.error("Auth error:", error);
          return null;
        }
      }
    }),
  ],
  pages: {
    signIn: '/signin',
    error: '/auth/error',
  },
  callbacks: {
    async signIn({ user, account, profile }) {
      if (!user?.email) {
        return false;
      }

      try {
        await connectToDatabase();

        // Check if user exists
        let dbUser = await User.findOne({ email: user.email });

        if (!dbUser) {
          // Create new user if doesn't exist
          const userData = {
            email: user.email,
            name: user.name,
            image: user.image,
            provider: account?.provider,
            firstName: user.name?.split(' ')[0] || '',
            lastName: user.name?.split(' ').slice(1).join(' ') || '',
          };

          dbUser = await User.create(userData);
        } else {
          // Update existing user's info
          await User.findOneAndUpdate(
            { email: user.email },
            {
              $set: {
                name: user.name,
                image: user.image,
                provider: account?.provider,
                lastLogin: new Date(),
              },
            },
            { new: true }
          );
        }

        return true;
      } catch (error) {
        console.error("Error in signIn callback:", error);
        return false;
      }
    },
    async jwt({ token, user, account }) {
      if (account && user) {
        token.accessToken = account.access_token;
        token.id = user.id;
        token.provider = account.provider;
        
        // Add user role to token if available
        if ('role' in user) {
          token.role = user.role;
        }
      }
      return token;
    },
    async session({ session, token }) {
      try {
        if (session.user) {
          // Add token data to session
          if (token.id) {
            session.user.id = token.id;
          }
          
          // Add role from token if available
          if (token.role) {
            session.user.role = token.role;
          }
          
          await connectToDatabase();
          const user = await User.findOne({ email: session.user.email }).lean() as { _id: string, provider?: string, role?: string } | null;
          
          if (user) {
            if (user && !Array.isArray(user) && '_id' in user) {
              session.user.id = user._id.toString();
            }
            if (user && !Array.isArray(user) && 'provider' in user) {
              session.user.provider = user.provider;
            }
            // Make sure role is included
            if (user && !Array.isArray(user) && 'role' in user) {
              session.user.role = user.role;
            }
          }
          
          console.log("Session callback - user role:", session.user.role);
        }
        return session;
      } catch (error) {
        console.error("Session error:", error);
        return session;
      }
    }
  },
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: 'jwt',
  },
  debug: process.env.NODE_ENV === "development",
};

declare module "next-auth" {
  interface Session {
    user: {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      id?: string;
      provider?: string;
      role?: string; // Add role field
    };
  }
}

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };



