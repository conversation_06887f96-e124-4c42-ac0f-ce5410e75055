import { NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import Problem from "@/models/problem";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { uploadToCloudinary } from "@/lib/cloudinary";

export async function POST(request: Request) {
  try {
    // Check if user is authenticated and is an admin
    const session = await getServerSession(authOptions);

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { message: "Unauthorized: Admin access required" },
        { status: 403 }
      );
    }

    // Connect to database
    await connectToDatabase();

    // Parse form data instead of JSON
    const formData = await request.formData();

    // Extract fields from form data
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const difficulty = formData.get('difficulty') as string;
    const topics = JSON.parse(formData.get('topics') as string);
    const constraints = JSON.parse(formData.get('constraints') as string);
    const testCases = JSON.parse(formData.get('testCases') as string);

    // Validate required fields
    if (!title || !description || !difficulty) {
      return NextResponse.json(
        { message: "Missing required fields" },
        { status: 400 }
      );
    }

    // Handle image uploads to Cloudinary
    const imageUrls = [];
    for (let [key, value] of formData.entries()) {
      if (key.startsWith('image-') && value instanceof Blob) {
        try {
          // Convert file to buffer
          const bytes = await value.arrayBuffer();
          const buffer = Buffer.from(bytes);

          // Upload to Cloudinary
          const result = await uploadToCloudinary(buffer, 'problems');

          // Store the Cloudinary URL
          imageUrls.push(result.secure_url);
          console.log(`Uploaded image to Cloudinary: ${result.secure_url}`);
        } catch (uploadError) {
          console.error("Error uploading image to Cloudinary:", uploadError);
        }
      }
    }

    // Create problem object
    const problemData = {
      title,
      description,
      difficulty,
      topics,
      constraints,
      testCases,
      imageUrls, // Changed from images to imageUrls to match the model
      createdBy: session.user.id
    };

    // Create new problem
    try {
      console.log("Creating problem with data:", JSON.stringify(problemData, null, 2));

      // Let MongoDB generate the _id
      const newProblem = new Problem(problemData);

      // Log the problem instance before saving
      console.log("Problem instance before save:", newProblem);

      const savedProblem = await newProblem.save();
      console.log("Problem saved successfully:", savedProblem._id);

      return NextResponse.json(
        { message: "Problem created successfully", problem: savedProblem },
        { status: 201 }
      );
    } catch (saveError) {
      console.error("Error saving problem:", saveError);
      // Log the full error object for debugging
      console.error("Full error object:", JSON.stringify(saveError, Object.getOwnPropertyNames(saveError), 2));

      return NextResponse.json(
        { message: "Error saving problem", error: saveError instanceof Error ? saveError.message : "Unknown error" },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error("Error creating problem:", error);
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      { message: "Error creating problem", error: errorMessage },
      { status: 500 }
    );
  }
}
