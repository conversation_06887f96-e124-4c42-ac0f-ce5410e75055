import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import Problem from "@/models/problem";
import mongoose from "mongoose";

export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    await connectToDatabase();

    // Await the params object before accessing its properties
    const { id } = await context.params;

    // Log the ID we're trying to find
    console.log("Looking for problem with ID:", id);

    // Check if the ID is a valid ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      console.error("Invalid ObjectId format:", id);
      return NextResponse.json(
        { message: "Invalid problem ID format" },
        { status: 400 }
      );
    }

    // Find the problem by ID
    const problem = await Problem.findById(id).lean();

    if (!problem) {
      console.error("Problem not found with ID:", id);
      return NextResponse.json(
        { message: "Problem not found" },
        { status: 404 }
      );
    }

    console.log("Found problem:", problem.title);
    return NextResponse.json(problem);
  } catch (error) {
    console.error("Error fetching problem:", error);
    return NextResponse.json(
      { message: "Error fetching problem", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
