import { NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import Problem from "@/models/problem";

export async function GET() {
  try {
    await connectToDatabase();

    // Get all problems from the database
    const problems = await Problem.find()
      .select('_id title difficulty topics')
      .sort({ createdAt: -1 }) // Sort by creation date, newest first
      .lean();

    return NextResponse.json(problems);
  } catch (error) {
    console.error("Error fetching problems:", error);
    return NextResponse.json(
      { message: "Error fetching problems", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}