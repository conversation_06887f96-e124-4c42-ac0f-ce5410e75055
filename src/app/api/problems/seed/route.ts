import { NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import Problem from "@/models/problem";

export async function GET() {
  try {
    await connectToDatabase();

    // Fetch all problems from the database
    const problems = await Problem.find()
      .select('_id title difficulty topics')
      .sort({ createdAt: -1 })
      .lean();

    return NextResponse.json({
      message: `Found ${problems.length} problems in the database`,
      count: problems.length,
      problems: problems
    });
  } catch (error) {
    console.error("Error fetching problems:", error);
    return NextResponse.json(
      { message: "Error fetching problems", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    );
  }
}
