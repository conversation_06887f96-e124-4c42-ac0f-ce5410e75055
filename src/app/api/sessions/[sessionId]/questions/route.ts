import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Session from '@/models/session';
import Question from '@/models/question';

export async function GET(
  request: NextRequest,
  context: { params: { sessionId: string } }
) {
  try {
    await connectToDatabase();

    // Await the params object before accessing its properties
    const { sessionId } = await context.params;

    // First, get the session to verify it exists
    const session = await Session.findById(sessionId).lean();

    if (!session) {
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      );
    }

    // Get questions for this session
    const questions = await Question.find({ sessionId }).lean();

    return NextResponse.json(questions);
  } catch (error) {
    console.error('Error fetching questions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch questions' },
      { status: 500 }
    );
  }
}
