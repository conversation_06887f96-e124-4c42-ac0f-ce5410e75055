import { NextRequest, NextResponse } from "next/server";
import mongoose from "mongoose";
import { connectToDatabase } from "@/lib/db";
import Session from "@/models/session";
import Question from "@/models/question";

export async function POST(req: NextRequest) {
  try {
    await connectToDatabase();
    
    const data = await req.json();
    
    // Start a session for transaction
    const mongoSession = await mongoose.startSession();
    let result;
    
    try {
      // Start transaction
      await mongoSession.withTransaction(async () => {
        // Create the session
        const newSession = new Session(data);
        await newSession.save({ session: mongoSession });
        
        result = newSession;
      });
      
      return NextResponse.json(result, { status: 201 });
    } catch (error) {
      console.error("Transaction error:", error);
      return NextResponse.json(
        { message: "Failed to create session", error: error instanceof Error ? error.message : String(error) },
        { status: 500 }
      );
    } finally {
      // End the session
      await mongoSession.endSession();
    }
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { message: "Failed to create session", error: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
