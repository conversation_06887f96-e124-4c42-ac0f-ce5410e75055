import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import CodingSubmission from "@/models/codingSubmission";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user || !(session.user as { id: string }).id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    await connectToDatabase();
    
    // Await the params object before accessing its properties
    const { id } = await context.params;
    
    // Get user's submissions for this specific problem
    const submissions = await CodingSubmission.find({ 
      userId: (session.user as { id: string }).id,
      problemId: id 
    })
      .sort({ createdAt: -1 })
      .lean();

    return NextResponse.json(submissions);
  } catch (error) {
    console.error("Error fetching problem submissions:", error);
    return NextResponse.json(
      { message: "Error fetching problem submissions" },
      { status: 500 }
    );
  }
} 