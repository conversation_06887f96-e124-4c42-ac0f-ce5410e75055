import { NextRequest, NextResponse } from "next/server";
import { executeCode, STATUS_CODES, STATUS_DESCRIPTIONS } from "@/lib/codeExecution";
import { connectToDatabase } from "@/lib/db";
import Problem from "@/models/problem";

/**
 * API endpoint for executing code without saving it
 * This is used for the "Run Code" functionality
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body
    const { code, language, testCase, problemId } = await req.json();

    // Validate required fields
    if (!code) {
      return NextResponse.json({
        results: {
          status: "Compilation Error",
          statusCode: STATUS_CODES.COMPILATION_ERROR,
          output: "",
          error: "Code cannot be empty",
          executionTime: null,
          memory: null,
        }
      });
    }

    if (!language) {
      return NextResponse.json({
        results: {
          status: "Internal Error",
          statusCode: STATUS_CODES.INTERNAL_ERROR,
          output: "",
          error: "Programming language must be specified",
          executionTime: null,
          memory: null,
        }
      });
    }

    // If a specific test case is provided, execute only that test case
    if (testCase) {
      const input = testCase.input || "";
      const expectedOutput = testCase.output || "";

      console.log(`API: Executing ${language} code with input: ${input}`);

      try {
        const result = await executeCode(code, language, input);
        console.log('Execution result:', result);

        // Only compare outputs if we have expected output and execution was successful
        if (expectedOutput && result.status === STATUS_CODES.ACCEPTED) {
          // More robust output comparison
          const normalizeOutput = (output: string) => {
            return output
              .trim()                           // Remove leading/trailing whitespace
              .replace(/\r\n/g, '\n')          // Normalize line endings
              .replace(/\n+$/, '')             // Remove trailing newlines
              .replace(/\s+$/gm, '')           // Remove trailing spaces from each line
              .toLowerCase();                   // Convert to lowercase for comparison
          };

          const normalizedActual = normalizeOutput(result.output);
          const normalizedExpected = normalizeOutput(expectedOutput);

          console.log('Normalized actual:', JSON.stringify(normalizedActual));
          console.log('Normalized expected:', JSON.stringify(normalizedExpected));

          if (normalizedActual === normalizedExpected) {
            result.status = STATUS_CODES.ACCEPTED;
            result.statusDescription = STATUS_DESCRIPTIONS[STATUS_CODES.ACCEPTED];
          } else {
            result.status = STATUS_CODES.WRONG_ANSWER;
            result.statusDescription = STATUS_DESCRIPTIONS[STATUS_CODES.WRONG_ANSWER];
          }
        }

        return NextResponse.json({
          results: {
            status: result.statusDescription,
            statusCode: result.status,
            input: input,
            output: result.output,
            expectedOutput: expectedOutput,
            error: result.error,
            executionTime: result.executionTime,
            memory: result.memory,
            explanation: testCase?.explanation,
          }
        });
      } catch (execError) {
        console.error("Code execution error:", execError);
        return NextResponse.json({
          results: {
            status: "Runtime Error",
            statusCode: STATUS_CODES.RUNTIME_ERROR,
            input: input,
            output: "",
            expectedOutput: expectedOutput,
            error: execError instanceof Error ? execError.message : "Unknown execution error",
            executionTime: null,
            memory: null,
            explanation: testCase?.explanation,
          }
        });
      }
    }

    // If neither testCase nor problemId is provided, return an error
    if (!testCase && !problemId) {
      return NextResponse.json({
        results: {
          status: "Internal Error",
          statusCode: STATUS_CODES.INTERNAL_ERROR,
          output: "",
          error: "Either testCase or problemId must be provided",
          executionTime: null,
          memory: null,
        }
      });
    }

    // If problemId is provided, run against all test cases (LeetCode style)
    if (problemId) {
      try {
        // Connect to database
        await connectToDatabase();

        // Get the problem to access test cases
        const problem = await Problem.findById(problemId).lean();

        if (!problem) {
          return NextResponse.json({
            results: {
              status: "Internal Error",
              statusCode: STATUS_CODES.INTERNAL_ERROR,
              output: "",
              error: "Problem not found",
              executionTime: null,
              memory: null,
            }
          });
        }

        // Execute code against all test cases
        const testCases = problem.testCases || [];
        let allTestsPassed = true;
        const testResults = [];

        // If there are no test cases, return an error
        if (testCases.length === 0) {
          return NextResponse.json({
            results: {
              status: "Internal Error",
              statusCode: STATUS_CODES.INTERNAL_ERROR,
              output: "",
              error: "No test cases found for this problem",
              executionTime: null,
              memory: null,
            }
          });
        }

        // First check for compilation errors with the first test case
        const compilationCheck = await executeCode(code, language, testCases[0].input);
        if (compilationCheck.status === STATUS_CODES.COMPILATION_ERROR) {
          return NextResponse.json({
            results: {
              status: compilationCheck.statusDescription,
              statusCode: compilationCheck.status,
              output: "",
              error: compilationCheck.error,
              executionTime: null,
              memory: null,
              testResults: [],
            }
          });
        }

        // Execute all test cases
        for (const testCase of testCases) {
          try {
            console.log(`Executing test case with input: ${testCase.input}`);
            const result = await executeCode(code, language, testCase.input);

            // Only proceed with comparison if execution was successful
            if (result.status === STATUS_CODES.ACCEPTED) {
              // More robust output comparison
              const normalizeOutput = (output: string) => {
                return output
                  .trim()                           // Remove leading/trailing whitespace
                  .replace(/\r\n/g, '\n')          // Normalize line endings
                  .replace(/\n+$/, '')             // Remove trailing newlines
                  .replace(/\s+$/gm, '')           // Remove trailing spaces from each line
                  .toLowerCase();                   // Convert to lowercase for comparison
              };

              const normalizedActual = normalizeOutput(result.output);
              const normalizedExpected = normalizeOutput(testCase.output);

              console.log(`Test case ${testCases.indexOf(testCase) + 1}:`);
              console.log('Normalized actual:', JSON.stringify(normalizedActual));
              console.log('Normalized expected:', JSON.stringify(normalizedExpected));

              if (normalizedActual === normalizedExpected) {
                result.status = STATUS_CODES.ACCEPTED;
                result.statusDescription = STATUS_DESCRIPTIONS[STATUS_CODES.ACCEPTED];
              } else {
                result.status = STATUS_CODES.WRONG_ANSWER;
                result.statusDescription = STATUS_DESCRIPTIONS[STATUS_CODES.WRONG_ANSWER];
                allTestsPassed = false;
              }
            } else {
              // If execution failed, mark as failed
              allTestsPassed = false;
            }

            testResults.push({
              input: testCase.input,
              expectedOutput: testCase.output,
              actualOutput: result.output,
              status: result.statusDescription,
              statusCode: result.status,
              error: result.error,
              executionTime: result.executionTime,
              memory: result.memory,
              explanation: testCase.explanation,
            });
          } catch (testError) {
            console.error("Error executing test case:", testError);
            allTestsPassed = false;

            testResults.push({
              input: testCase.input,
              expectedOutput: testCase.output,
              actualOutput: "",
              status: "Runtime Error",
              statusCode: STATUS_CODES.RUNTIME_ERROR,
              error: testError instanceof Error ? testError.message : "Unknown test execution error",
              executionTime: null,
              memory: null,
              explanation: testCase.explanation,
            });
          }
        }

        return NextResponse.json({
          results: {
            status: allTestsPassed ? "Accepted" : "Failed",
            statusCode: allTestsPassed ? STATUS_CODES.ACCEPTED : STATUS_CODES.WRONG_ANSWER,
            testResults,
          }
        });
      } catch (error) {
        console.error('Error executing code against all test cases:', error);
        return NextResponse.json({
          results: {
            status: "Internal Error",
            statusCode: STATUS_CODES.INTERNAL_ERROR,
            output: "",
            error: error instanceof Error ? error.message : "An error occurred while executing your code",
            executionTime: null,
            memory: null,
          }
        });
      }
    }
  } catch (error) {
    console.error("API error executing code:", error);
    return NextResponse.json({
      results: {
        status: "Internal Error",
        statusCode: STATUS_CODES.INTERNAL_ERROR,
        output: "",
        error: error instanceof Error ? error.message : "Unknown error occurred",
        executionTime: null,
        memory: null,
      }
    });
  }
}
