import { NextRequest, NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import CodingSubmission from "@/models/codingSubmission";
import Problem from "@/models/problem";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";
import { executeCode, STATUS_CODES } from "@/lib/codeExecution";

/**
 * API endpoint for submitting code solutions
 * This is used for the "Submit Solution" functionality
 */
export async function POST(req: NextRequest) {
  try {
    // Check if user is authenticated
    const session = await getServerSession(authOptions);

    if (!session || !session.user || !(session.user as { id: string }).id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const { problemId, code, language } = await req.json();

    // Validate required fields
    if (!problemId) {
      return NextResponse.json(
        { message: "Problem ID is required" },
        { status: 400 }
      );
    }

    if (!code) {
      return NextResponse.json({
        results: {
          status: "Compilation Error",
          statusCode: STATUS_CODES.COMPILATION_ERROR,
          error: "Code cannot be empty",
          testResults: [],
        }
      });
    }

    if (!language) {
      return NextResponse.json({
        results: {
          status: "Internal Error",
          statusCode: STATUS_CODES.INTERNAL_ERROR,
          error: "Programming language must be specified",
          testResults: [],
        }
      });
    }

    // Connect to database
    await connectToDatabase();

    // Get the problem to access test cases
    const problem = await Problem.findById(problemId).lean();

    if (!problem) {
      return NextResponse.json(
        { message: "Problem not found" },
        { status: 404 }
      );
    }

    console.log(`Submitting ${language} solution for problem: ${problemId}`);

    // Execute code against all test cases
    const testCases = problem.testCases || [];
    let allTestsPassed = true;
    const testResults = [];

    // If there are no test cases, create a default one
    if (testCases.length === 0) {
      console.log('No test cases found, using a default test case');
      testCases.push({
        input: "sample input",
        output: "sample output",
      });
    }

    for (const testCase of testCases) {
      try {
        console.log(`Executing test case with input: ${testCase.input}`);
        const result = await executeCode(code, language, testCase.input);

        // Only proceed with comparison if execution was successful
        if (result.status === STATUS_CODES.ACCEPTED) {
          // More robust output comparison
          const normalizeOutput = (output: string) => {
            return output
              .trim()                           // Remove leading/trailing whitespace
              .replace(/\r\n/g, '\n')          // Normalize line endings
              .replace(/\n+$/, '')             // Remove trailing newlines
              .replace(/\s+$/gm, '')           // Remove trailing spaces from each line
              .toLowerCase();                   // Convert to lowercase for comparison
          };

          const normalizedActual = normalizeOutput(result.output);
          const normalizedExpected = normalizeOutput(testCase.output);

          console.log(`Submission test case ${testCases.indexOf(testCase) + 1}:`);
          console.log('Normalized actual:', JSON.stringify(normalizedActual));
          console.log('Normalized expected:', JSON.stringify(normalizedExpected));

          if (normalizedActual === normalizedExpected) {
            result.status = STATUS_CODES.ACCEPTED;
            result.statusDescription = "Accepted";
          } else {
            result.status = STATUS_CODES.WRONG_ANSWER;
            result.statusDescription = "Wrong Answer";
            allTestsPassed = false;
          }
        } else {
          // If execution failed, mark as failed
          allTestsPassed = false;
        }

        testResults.push({
          input: testCase.input,
          expectedOutput: testCase.output,
          actualOutput: result.output,
          status: result.statusDescription,
          statusCode: result.status,
          error: result.error,
          executionTime: result.executionTime,
          memory: result.memory,
        });
      } catch (testError) {
        console.error("Error executing test case:", testError);
        allTestsPassed = false;

        testResults.push({
          input: testCase.input,
          expectedOutput: testCase.output,
          actualOutput: "",
          status: "Runtime Error",
          statusCode: STATUS_CODES.RUNTIME_ERROR,
          error: testError instanceof Error ? testError.message : "Unknown test execution error",
          executionTime: null,
          memory: null,
        });
      }
    }

    try {
      // Create submission record
      const submission = new CodingSubmission({
        userId: (session.user as { id: string }).id,
        problemId,
        code,
        language,
        results: {
          status: allTestsPassed ? "Accepted" : "Failed",
          testResults,
        },
      });

      await submission.save();
      console.log(`Submission saved with ID: ${submission._id}`);
    } catch (dbError) {
      console.error("Error saving submission to database:", dbError);
      // Continue execution to return results even if saving fails
    }

    return NextResponse.json({
      message: "Submission successful",
      results: {
        status: allTestsPassed ? "Accepted" : "Failed",
        testResults,
      },
    });
  } catch (error) {
    console.error("Error submitting solution:", error);
    return NextResponse.json({
      results: {
        status: "Internal Error",
        statusCode: STATUS_CODES.INTERNAL_ERROR,
        error: error instanceof Error ? error.message : "Unknown error occurred",
        testResults: [],
      }
    });
  }
}
