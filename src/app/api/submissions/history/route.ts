import { NextResponse } from "next/server";
import { connectToDatabase } from "@/lib/db";
import CodingSubmission from "@/models/codingSubmission";
import Problem from "@/models/problem";
import { getServerSession } from "next-auth";
import { authOptions } from "@/app/api/auth/[...nextauth]/route";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user || !(session.user as { id: string }).id) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }
    
    await connectToDatabase();
    
    // Get user's submissions
    const submissions = await CodingSubmission.find({ userId: (session.user as { id: string }).id })
      .sort({ createdAt: -1 })
      .lean();
    
    // Get problem titles
    const problemIds = submissions.map(sub => sub.problemId);
    const problems = await Problem.find({ _id: { $in: problemIds } })
      .select('_id title')
      .lean();
    
    // Create a map of problem IDs to titles
    const problemMap = problems.reduce((map, problem) => {
      map[problem._id as string] = problem.title;
      return map;
    }, {} as Record<string, string>);
    
    // Add problem titles to submissions
    const submissionsWithTitles = submissions.map(sub => ({
      ...sub,
      problemTitle: problemMap[sub.problemId] || 'Unknown Problem'
    }));

    return NextResponse.json(submissionsWithTitles);
  } catch (error) {
    console.error("Error fetching submission history:", error);
    return NextResponse.json(
      { message: "Error fetching submission history" },
      { status: 500 }
    );
  }
}
