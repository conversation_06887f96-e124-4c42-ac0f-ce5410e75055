import { NextRequest, NextResponse } from 'next/server';
import { checkSystemRequirements } from '@/lib/codeExecution';

export async function GET(request: NextRequest) {
  try {
    console.log('Checking system requirements...');
    
    const requirements = await checkSystemRequirements();
    
    console.log('System requirements check result:', requirements);
    
    return NextResponse.json({
      success: true,
      requirements,
      message: 'System requirements checked successfully'
    });
  } catch (error) {
    console.error('Error checking system requirements:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to check system requirements'
    }, { status: 500 });
  }
} 