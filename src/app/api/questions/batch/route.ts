import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/db';
import Question from '@/models/question';
import mongoose from 'mongoose';
import Session from '@/models/session';

export async function POST(request: NextRequest) {
  console.log('API: Questions batch endpoint called');
  
  try {
    console.log('API: Connecting to database');
    await connectToDatabase();
    
    console.log('API: Parsing request body');
    const questions = await request.json();
    
    console.log('API: Received questions data:', JSON.stringify(questions).substring(0, 200));
    
    if (!Array.isArray(questions) || questions.length === 0) {
      console.error('API: Invalid questions data received:', questions);
      return NextResponse.json(
        { error: 'Invalid questions data' },
        { status: 400 }
      );
    }
    
    // Process each question to ensure proper format
    const processedQuestions = questions.map(q => {
      // Create a new object to avoid modifying the original
      const processedQ = { ...q };
      
      // Convert sessionId to ObjectId if it's a string
      if (typeof processedQ.sessionId === 'string') {
        try {
          processedQ.sessionId = new mongoose.Types.ObjectId(processedQ.sessionId);
        } catch (error) {
          console.error(`API: Invalid sessionId format: ${processedQ.sessionId}`, error);
          throw new Error(`Invalid sessionId format: ${processedQ.sessionId}`);
        }
      }
      
      return processedQ;
    });
    
    // Use insertMany to batch insert the questions
    console.log('API: Inserting questions into database');
    const result = await Question.insertMany(processedQuestions);
    
    return NextResponse.json({ success: true, count: result.length, questions: result });
  } catch (error) {
    console.error('API: Error storing questions:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to store questions' },
      { status: 500 }
    );
  }
}
