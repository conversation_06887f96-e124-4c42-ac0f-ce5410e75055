@import 'tailwindcss';

@custom-variant dark (&:is(.dark *));

@theme {
  --font-*: initial;
  --font-outfit: Outfit, sans-serif;

  --breakpoint-*: initial;
  --breakpoint-2xsm: 375px;
  --breakpoint-xsm: 425px;
  --breakpoint-3xl: 2000px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  --text-title-2xl: 72px;
  --text-title-2xl--line-height: 90px;
  --text-title-xl: 60px;
  --text-title-xl--line-height: 72px;
  --text-title-lg: 48px;
  --text-title-lg--line-height: 60px;
  --text-title-md: 36px;
  --text-title-md--line-height: 44px;
  --text-title-sm: 30px;
  --text-title-sm--line-height: 38px;
  --text-theme-xl: 20px;
  --text-theme-xl--line-height: 30px;
  --text-theme-sm: 14px;
  --text-theme-sm--line-height: 20px;
  --text-theme-xs: 12px;
  --text-theme-xs--line-height: 18px;

  --color-current: currentColor;
  --color-transparent: transparent;
  --color-white: #ffffff;
  --color-black: #101828;

  --color-brand-25: #f2f7ff;
  --color-brand-50: #ecf3ff;
  --color-brand-100: #dde9ff;
  --color-brand-200: #c2d6ff;
  --color-brand-300: #9cb9ff;
  --color-brand-400: #7592ff;
  --color-brand-500: #465fff;
  --color-brand-600: #3641f5;
  --color-brand-700: #2a31d8;
  --color-brand-800: #252dae;
  --color-brand-900: #262e89;
  --color-brand-950: #161950;

  --color-blue-light-25: #f5fbff;
  --color-blue-light-50: #f0f9ff;
  --color-blue-light-100: #e0f2fe;
  --color-blue-light-200: #b9e6fe;
  --color-blue-light-300: #7cd4fd;
  --color-blue-light-400: #36bffa;
  --color-blue-light-500: #0ba5ec;
  --color-blue-light-600: #0086c9;
  --color-blue-light-700: #026aa2;
  --color-blue-light-800: #065986;
  --color-blue-light-900: #0b4a6f;
  --color-blue-light-950: #062c41;

  --color-gray-25: #fcfcfd;
  --color-gray-50: #f9fafb;
  --color-gray-100: #f2f4f7;
  --color-gray-200: #e4e7ec;
  --color-gray-300: #d0d5dd;
  --color-gray-400: #98a2b3;
  --color-gray-500: #667085;
  --color-gray-600: #475467;
  --color-gray-700: #344054;
  --color-gray-800: #1d2939;
  --color-gray-900: #101828;
  --color-gray-950: #0c111d;
  --color-gray-dark: #1a2231;

  --color-orange-25: #fffaf5;
  --color-orange-50: #fff6ed;
  --color-orange-100: #ffead5;
  --color-orange-200: #fddcab;
  --color-orange-300: #feb273;
  --color-orange-400: #fd853a;
  --color-orange-500: #fb6514;
  --color-orange-600: #ec4a0a;
  --color-orange-700: #c4320a;
  --color-orange-800: #9c2a10;
  --color-orange-900: #7e2410;
  --color-orange-950: #511c10;

  --color-success-25: #f6fef9;
  --color-success-50: #ecfdf3;
  --color-success-100: #d1fadf;
  --color-success-200: #a6f4c5;
  --color-success-300: #6ce9a6;
  --color-success-400: #32d583;
  --color-success-500: #12b76a;
  --color-success-600: #039855;
  --color-success-700: #027a48;
  --color-success-800: #05603a;
  --color-success-900: #054f31;
  --color-success-950: #053321;

  --color-error-25: #fffbfa;
  --color-error-50: #fef3f2;
  --color-error-100: #fee4e2;
  --color-error-200: #fecdca;
  --color-error-300: #fda29b;
  --color-error-400: #f97066;
  --color-error-500: #f04438;
  --color-error-600: #d92d20;
  --color-error-700: #b42318;
  --color-error-800: #912018;
  --color-error-900: #7a271a;
  --color-error-950: #55160c;

  --color-warning-25: #fffcf5;
  --color-warning-50: #fffaeb;
  --color-warning-100: #fef0c7;
  --color-warning-200: #fedf89;
  --color-warning-300: #fec84b;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #93370d;
  --color-warning-900: #7a2e0e;
  --color-warning-950: #4e1d09;

  --color-theme-pink-500: #ee46bc;

  --color-theme-purple-500: #7a5af8;

  --shadow-theme-md:
    0px 4px 8px -2px rgba(16, 24, 40, 0.1),
    0px 2px 4px -2px rgba(16, 24, 40, 0.06);
  --shadow-theme-lg:
    0px 12px 16px -4px rgba(16, 24, 40, 0.08),
    0px 4px 6px -2px rgba(16, 24, 40, 0.03);
  --shadow-theme-sm:
    0px 1px 3px 0px rgba(16, 24, 40, 0.1),
    0px 1px 2px 0px rgba(16, 24, 40, 0.06);
  --shadow-theme-xs: 0px 1px 2px 0px rgba(16, 24, 40, 0.05);
  --shadow-theme-xl:
    0px 20px 24px -4px rgba(16, 24, 40, 0.08),
    0px 8px 8px -4px rgba(16, 24, 40, 0.03);
  --shadow-datepicker: -5px 0 0 #262d3c, 5px 0 0 #262d3c;
  --shadow-focus-ring: 0px 0px 0px 4px rgba(70, 95, 255, 0.12);
  --shadow-slider-navigation:
    0px 1px 2px 0px rgba(16, 24, 40, 0.1), 0px 1px 3px 0px rgba(16, 24, 40, 0.1);
  --shadow-tooltip:
    0px 4px 6px -2px rgba(16, 24, 40, 0.05),
    -8px 0px 20px 8px rgba(16, 24, 40, 0.05);

  --drop-shadow-4xl:
    0 35px 35px rgba(0, 0, 0, 0.25), 0 45px 65px rgba(0, 0, 0, 0.15);

  --z-index-1: 1;
  --z-index-9: 9;
  --z-index-99: 99;
  --z-index-999: 999;
  --z-index-9999: 9999;
  --z-index-99999: 99999;
  --z-index-999999: 999999;
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
    @apply dark:border-gray-700;
  }
  button:not(:disabled),
  [role="button"]:not(:disabled) {
    cursor: pointer;
  }
  body {
    @apply relative font-normal font-outfit z-1 bg-gray-50 dark:bg-gray-900 dark:text-gray-100;
  }
}

/* Professional Dark Mode Theme - Consistent Background Colors */
@layer utilities {
  /* Base dark mode colors */
  .dark body {
    @apply bg-gray-900 text-white;
  }

  /* Background colors - ensuring consistency */
  .dark .bg-white,
  .dark .bg-gray-50,
  .dark .bg-gray-100 {
    @apply bg-gray-900;
  }

  /* Card backgrounds - ensuring consistency */
  .dark .card,
  .dark .selection-card,
  .dark [class*="job-card"],
  .dark [class*="selection-option"] {
    @apply bg-gray-800 border-gray-700;
  }

  /* Form containers and sections */
  .dark .form-section,
  .dark .form-container,
  .dark [class*="form-panel"] {
    @apply bg-gray-800 border-gray-700;
  }

  /* Job profile section */
  .dark .job-profile-section,
  .dark [class*="profile-section"] {
    @apply bg-gray-800 border-gray-700;
  }

  /* Improve form elements contrast in dark mode */
  .dark select,
  .dark input,
  .dark textarea,
  .dark .select-wrapper {
    @apply bg-gray-800 border-gray-700 text-white;
  }

  /* Button styling for dark mode */
  .dark button:not([class*="bg-"]) {
    @apply bg-gray-800 text-white hover:bg-gray-700;
  }

  /* Primary button in dark mode */
  .dark button.bg-brand-500 {
    @apply bg-brand-600 hover:bg-brand-700 text-white;
  }

  /* Sidebar and navigation elements */
  .dark .sidebar,
  .dark nav,
  .dark .nav-item {
    @apply bg-gray-900 text-white;
  }

  /* Tags and badges in job cards */
  .dark [class*="tag-"],
  .dark [class*="badge-"] {
    @apply bg-gray-700 text-gray-300;
  }

  /* Text colors */
  .dark h1,
  .dark h2,
  .dark h3,
  .dark h4,
  .dark h5,
  .dark h6,
  .dark p,
  .dark span:not([class*="bg-"]) {
    @apply text-white;
  }

  /* Secondary text */
  .dark .text-gray-500,
  .dark .text-gray-600,
  .dark .text-gray-700 {
    @apply text-gray-400;
  }
}

@utility menu-item {
  @apply relative flex items-center w-full gap-3 px-3 py-2 font-medium rounded-lg text-theme-sm;
}

@utility menu-item-active {
  @apply bg-brand-50 text-brand-500 dark:bg-brand-500/[0.12] dark:text-brand-400;
}

@utility menu-item-inactive {
  @apply text-gray-700 hover:bg-gray-100 group-hover:text-gray-700 dark:text-gray-300 dark:hover:bg-white/5 dark:hover:text-gray-300;
}

@utility menu-item-icon {
  @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400;
}

@utility menu-item-icon-active {
  @apply text-brand-500 dark:text-brand-400;
}

@utility menu-item-icon-inactive {
  @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300;
}

@utility menu-item-arrow {
  @apply relative;
}

@utility menu-item-arrow-active {
  @apply rotate-180 text-brand-500 dark:text-brand-400;
}

@utility menu-item-arrow-inactive {
  @apply text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300;
}

@utility menu-dropdown-item {
  @apply relative flex items-center gap-3 rounded-lg px-3 py-2.5 text-theme-sm font-medium;
}

@utility menu-dropdown-item-active {
  @apply bg-brand-50 text-brand-500 dark:bg-brand-500/[0.12] dark:text-brand-400;
}

@utility menu-dropdown-item-inactive {
  @apply text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-white/5;
}

@utility menu-dropdown-badge {
  @apply block rounded-full px-2.5 py-0.5 text-xs font-medium uppercase text-brand-500 dark:text-brand-400;
}

@utility menu-dropdown-badge-active {
  @apply bg-brand-100 dark:bg-brand-500/20;
}

@utility menu-dropdown-badge-inactive {
  @apply bg-brand-50 group-hover:bg-brand-100 dark:bg-brand-500/15 dark:group-hover:bg-brand-500/20;
}

@utility no-scrollbar {
  /* Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

@utility custom-scrollbar {
  /* For Webkit browsers (Chrome, Safari) */
  &::-webkit-scrollbar {
    @apply size-2;
  }

  &::-webkit-scrollbar-track {
    @apply rounded-full bg-transparent;
  }

  &::-webkit-scrollbar-thumb {
    @apply bg-gray-200 rounded-full dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600;
  }

  /* For Firefox */
  scrollbar-width: thin;
  scrollbar-color: #E4E7EC transparent; /* Light mode: thumb color track color */
}

/* Dark mode scrollbar for Firefox */
.dark .custom-scrollbar {
  scrollbar-color: #344054 transparent; /* Dark mode: thumb color track color */
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #344054;
}

/* Ensure consistent scrollbar styling for Monaco editor */
.monaco-editor .scrollbar .slider {
  @apply bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600;
  border-radius: 10px !important;
  opacity: 0.6 !important;
}

.monaco-editor .scrollbar .slider:hover {
  opacity: 0.8 !important;
}

/* Hide the ugly vertical divider between editor and scrollbar */
.monaco-editor .scrollbar.vertical .slider {
  margin-left: 0 !important;
}

.monaco-scrollable-element > .scrollbar.vertical {
  background: transparent !important;
  border-left: none !important;
}

@layer utilities {
  /* For Remove Date Icon */
  input[type="date"]::-webkit-inner-spin-button,
  input[type="time"]::-webkit-inner-spin-button,
  input[type="date"]::-webkit-calendar-picker-indicator,
  input[type="time"]::-webkit-calendar-picker-indicator {
    display: none;
    -webkit-appearance: none;
  }
}

/* third-party libraries CSS */
.apexcharts-legend-text {
  @apply text-gray-700! dark:text-gray-400!;
}

.apexcharts-text {
  @apply fill-gray-700! dark:fill-gray-400!;
}

.apexcharts-tooltip.apexcharts-theme-light {
  @apply gap-1 rounded-lg! border-gray-200! p-3 shadow-theme-sm! dark:border-gray-800! dark:bg-gray-900!;
}

.apexcharts-legend-text {
  @apply pl-5! text-gray-700! dark:text-gray-400!;
}
.apexcharts-tooltip-series-group {
  @apply p-0!;
}
.apexcharts-tooltip-y-group {
  @apply p-0!;
}
.apexcharts-tooltip-title {
  @apply mb-0! border-b-0! bg-transparent! p-0! text-[10px]! leading-4! text-gray-800! dark:text-white/90!;
}
.apexcharts-tooltip-text {
  @apply text-theme-xs! text-gray-700! dark:text-white/90!;
}
.apexcharts-tooltip-text-y-value {
  @apply font-medium!;
}

.apexcharts-gridline {
  @apply stroke-gray-100! dark:stroke-gray-800!;
}

/* Custom date input styles */
input[type="date"] {
  color-scheme: light dark;
}

/* Customize the calendar icon */
input[type="date"]::-webkit-calendar-picker-indicator {
  background: transparent;
  bottom: 0;
  color: transparent;
  cursor: pointer;
  height: 100%;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  opacity: 0;
}

/* Style for date input in dark mode */
.dark input[type="date"] {
  color-scheme: dark;
}

/* Placeholder styling for date inputs */
input[type="date"]::placeholder {
  color: #94a3b8;
}

.dark input[type="date"]::placeholder {
  color: #64748b;
}

/* Focus styles */
input[type="date"]:focus {
  border-color: #465fff;
  box-shadow: 0 0 0 2px rgba(70, 95, 255, 0.2);
}

.dark input[type="date"]:focus {
  border-color: #818cf8;
  box-shadow: 0 0 0 2px rgba(129, 140, 248, 0.2);
}

.fc .fc-view-harness {
  @apply max-w-full overflow-x-auto custom-scrollbar;
}
.fc-dayGridMonth-view.fc-view.fc-daygrid {
  @apply min-w-[718px];
}
.fc .fc-scrollgrid-section > * {
  border-right-width: 0;
  border-bottom-width: 0;
}
.fc .fc-scrollgrid {
  border-left-width: 0;
}
.fc .fc-toolbar.fc-header-toolbar {
  @apply flex-col gap-4 px-6 pt-6 sm:flex-row;
}
.fc-button-group {
  @apply gap-2;
}
.fc-button-group .fc-button {
  @apply flex h-10 w-10 items-center justify-center rounded-lg! border border-gray-200 bg-transparent hover:border-gray-200 hover:bg-gray-50 focus:shadow-none active:border-gray-200! active:bg-transparent! active:shadow-none! dark:border-gray-800 dark:hover:border-gray-800 dark:hover:bg-gray-900 dark:active:border-gray-800!;
}

.fc-button-group .fc-button.fc-prev-button:before {
  @apply inline-block mt-1;
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.0068 6L9.75684 12.25L16.0068 18.5' stroke='%23344054' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.fc-button-group .fc-button.fc-next-button:before {
  @apply inline-block mt-1;
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.50684 19L15.7568 12.75L9.50684 6.5' stroke='%23344054' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.dark .fc-button-group .fc-button.fc-prev-button:before {
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M16.0068 6L9.75684 12.25L16.0068 18.5' stroke='%2398A2B3' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.dark .fc-button-group .fc-button.fc-next-button:before {
  content: url("data:image/svg+xml,%3Csvg width='25' height='24' viewBox='0 0 25 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.50684 19L15.7568 12.75L9.50684 6.5' stroke='%2398A2B3' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
}
.fc-button-group .fc-button .fc-icon {
  @apply hidden;
}
.fc-addEventButton-button {
  @apply rounded-lg! border-0! bg-brand-500! px-4! py-2.5! text-sm! font-medium! hover:bg-brand-600! focus:shadow-none!;
}
.fc-toolbar-title {
  @apply text-lg! font-medium! text-gray-800 dark:text-white/90;
}
.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child {
  @apply rounded-lg bg-gray-100 p-0.5 dark:bg-gray-900;
}
.fc-header-toolbar.fc-toolbar .fc-toolbar-chunk:last-child .fc-button {
  @apply h-auto! w-auto! rounded-md border-0! bg-transparent px-5! py-2! text-sm font-medium text-gray-500 hover:text-gray-700 focus:shadow-none! dark:text-gray-400;
}
.fc-header-toolbar.fc-toolbar
  .fc-toolbar-chunk:last-child
  .fc-button.fc-button-active {
  @apply text-gray-900 bg-white dark:bg-gray-800 dark:text-white;
}
.fc-theme-standard th {
  @apply border-x-0! border-t border-gray-200! bg-gray-50 text-left! dark:border-gray-800! dark:bg-gray-900;
}
.fc-theme-standard td,
.fc-theme-standard .fc-scrollgrid {
  @apply border-gray-200! dark:border-gray-800!;
}
.fc .fc-col-header-cell-cushion {
  @apply px-5! py-4! text-sm font-medium uppercase text-gray-400;
}
.fc .fc-daygrid-day.fc-day-today {
  @apply bg-transparent;
}
.fc .fc-daygrid-day {
  @apply p-2;
}
.fc .fc-daygrid-day.fc-day-today .fc-scrollgrid-sync-inner {
  @apply rounded-sm bg-gray-100 dark:bg-white/[0.03];
}
.fc .fc-daygrid-day-number {
  @apply p-3! text-sm font-medium text-gray-700 dark:text-gray-400;
}
.fc .fc-daygrid-day-top {
  @apply flex-row!;
}
.fc .fc-day-other .fc-daygrid-day-top {
  opacity: 1;
}
.fc .fc-day-other .fc-daygrid-day-top .fc-daygrid-day-number {
  @apply text-gray-400 dark:text-white/30;
}
.event-fc-color {
  @apply rounded-lg py-2.5 pl-4 pr-3;
}
.event-fc-color .fc-event-title {
  @apply p-0 text-sm font-normal text-gray-700;
}
.fc-daygrid-event-dot {
  @apply w-1 h-5 ml-0 mr-3 border-none rounded-sm;
}
.fc-event {
  @apply focus:shadow-none;
}
.fc-daygrid-event.fc-event-start {
  @apply ml-3!;
}
.event-fc-color.fc-bg-success {
  @apply border-success-50 bg-success-50;
}
.event-fc-color.fc-bg-danger {
  @apply border-error-50 bg-error-50;
}
.event-fc-color.fc-bg-primary {
  @apply border-brand-50 bg-brand-50;
}
.event-fc-color.fc-bg-warning {
  @apply border-orange-50 bg-orange-50;
}
.event-fc-color.fc-bg-success .fc-daygrid-event-dot {
  @apply bg-success-500;
}
.event-fc-color.fc-bg-danger .fc-daygrid-event-dot {
  @apply bg-error-500;
}
.event-fc-color.fc-bg-primary .fc-daygrid-event-dot {
  @apply bg-brand-500;
}
.event-fc-color.fc-bg-warning .fc-daygrid-event-dot {
  @apply bg-orange-500;
}
.fc-direction-ltr .fc-timegrid-slot-label-frame {
  @apply px-3 py-1.5 text-left text-sm font-medium text-gray-500 dark:text-gray-400;
}
.fc .fc-timegrid-axis-cushion {
  @apply text-sm font-medium text-gray-500 dark:text-gray-400;
}
.custom-calendar .fc-h-event {
  background-color: transparent;
  border: none;
  color: black;
}
.fc.fc-media-screen {
  @apply min-h-screen;
}

.input-date-icon::-webkit-inner-spin-button,
.input-date-icon::-webkit-calendar-picker-indicator {
  opacity: 0;
  -webkit-appearance: none;
}

.stocks-slider-outer .swiper-button-next:after,
.stocks-slider-outer .swiper-button-prev:after {
  @apply hidden;
}

.stocks-slider-outer .swiper-button-next,
.stocks-slider-outer .swiper-button-prev {
  @apply static! mt-0 h-8 w-9 rounded-full border dark:hover:bg-white/[0.05] border-gray-200 text-gray-700! transition hover:bg-gray-100 dark:border-white/[0.03] dark:bg-gray-800 dark:text-gray-400!  dark:hover:text-white/90!;
}

.stocks-slider-outer .swiper-button-next.swiper-button-disabled,
.stocks-slider-outer .swiper-button-prev.swiper-button-disabled {
  @apply bg-white opacity-50 dark:bg-gray-900;
}

.stocks-slider-outer .swiper-button-next svg,
.stocks-slider-outer .swiper-button-prev svg {
  @apply h-auto! w-auto!;
}

.swiper-button-prev svg,
.swiper-button-next svg {
  @apply h-auto! w-auto!;
}

.carouselTwo .swiper-button-next:after,
.carouselTwo .swiper-button-prev:after,
.carouselFour .swiper-button-next:after,
.carouselFour .swiper-button-prev:after {
  @apply hidden;
}
.carouselTwo .swiper-button-next.swiper-button-disabled,
.carouselTwo .swiper-button-prev.swiper-button-disabled,
.carouselFour .swiper-button-next.swiper-button-disabled,
.carouselFour .swiper-button-prev.swiper-button-disabled {
  @apply bg-white/60 opacity-100!;
}
.carouselTwo .swiper-button-next,
.carouselTwo .swiper-button-prev,
.carouselFour .swiper-button-next,
.carouselFour .swiper-button-prev {
  @apply h-10 w-10 rounded-full border-[0.5px] border-white/10 bg-white/90 text-gray-700! shadow-slider-navigation backdrop-blur-[10px];
}

.carouselTwo .swiper-button-prev,
.carouselFour .swiper-button-prev {
  @apply left-3! sm:left-4!;
}

.carouselTwo .swiper-button-next,
.carouselFour .swiper-button-next {
  @apply right-3! sm:right-4!;
}

.carouselThree .swiper-pagination,
.carouselFour .swiper-pagination {
  @apply bottom-3! left-1/2! inline-flex w-auto! -translate-x-1/2 items-center gap-1.5 rounded-[40px] border-[0.5px] border-white/10 bg-white/60 px-2 py-1.5 shadow-slider-navigation backdrop-blur-[10px] sm:bottom-5!;
}

.carouselThree .swiper-pagination-bullet,
.carouselFour .swiper-pagination-bullet {
  @apply m-0! h-2.5 w-2.5 bg-white opacity-100 shadow-theme-xs duration-200 ease-in-out;
}

.carouselThree .swiper-pagination-bullet-active,
.carouselFour .swiper-pagination-bullet-active {
  @apply w-6.5 rounded-xl;
}

.jvectormap-container {
  @apply bg-gray-50! dark:bg-gray-900!;
}
.jvectormap-region.jvectormap-element {
  @apply fill-gray-300! hover:fill-brand-500! dark:fill-gray-700! dark:hover:fill-brand-500!;
}
.jvectormap-marker.jvectormap-element {
  @apply stroke-gray-200! dark:stroke-gray-800!;
}
.jvectormap-tip {
  @apply bg-brand-500! border-none! px-2! py-1!;
}
.jvectormap-zoomin,
.jvectormap-zoomout {
  @apply hidden!;
}

.form-check-input:checked ~ span {
  @apply border-[6px] border-brand-500 bg-brand-500 dark:border-brand-500;
}

.taskCheckbox:checked ~ .box span {
  @apply opacity-100 bg-brand-500;
}
.taskCheckbox:checked ~ p {
  @apply text-gray-400 line-through;
}
.taskCheckbox:checked ~ .box {
  @apply border-brand-500 bg-brand-500 dark:border-brand-500;
}

.task {
  transition: all 0.2s ease; /* Smooth transition for visual effects */
}

.task {
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px 0 rgba(16, 24, 40, 0.1),
    0 1px 2px 0 rgba(16, 24, 40, 0.06);
  opacity: 0.8;
  cursor: grabbing; /* Changes the cursor to indicate dragging */
}

/* Job cards in interview page */
.dark [class*="job-card"] {
  @apply bg-gray-800 border-gray-700;
}

.dark [class*="job-card"] [class*="tag"] {
  @apply bg-gray-700 text-gray-300;
}

/* Generate Interview Questions button */
.dark .generate-button {
  @apply bg-brand-600 hover:bg-brand-700 text-white;
}

/* Device check page animations */
@keyframes pulse {
  0%, 100% { transform: scaleY(1); }
  50% { transform: scaleY(1.5); }
}

.animate-pulse {
  animation: pulse 1.5s infinite;
}

.delay-75 {
  animation-delay: 0.15s;
}

.delay-100 {
  animation-delay: 0.3s;
}

.delay-150 {
  animation-delay: 0.45s;
}

.delay-200 {
  animation-delay: 0.6s;
}

/* Custom scrollbar styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark theme scrollbar */
.dark .custom-scrollbar {
  scrollbar-color: #4b5563 #1f2937;
}

.dark .custom-scrollbar::-webkit-scrollbar-track {
  background: #1f2937;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: #4b5563;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Problem description styling */
.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  margin: 1rem 0;
  display: block;
}

.dark .prose img {
  border-color: #374151;
}

/* Code blocks in problem description */
.prose pre {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 1rem;
  overflow-x: auto;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.dark .prose pre {
  background-color: #1e293b;
  border-color: #475569;
  color: #e2e8f0;
}

.prose code {
  background-color: #f1f5f9;
  color: #1e293b;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-family: 'Menlo', 'Monaco', 'Courier New', monospace;
  font-size: 0.875em;
}

.dark .prose code {
  background-color: #334155;
  color: #e2e8f0;
}

/* Lists in problem description */
.prose ul, .prose ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.prose li {
  margin: 0.5rem 0;
}

/* Tables in problem description */
.prose table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  overflow: hidden;
}

.dark .prose table {
  border-color: #374151;
}

.prose th, .prose td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.dark .prose th, .dark .prose td {
  border-color: #374151;
}

.prose th {
  background-color: #f9fafb;
  font-weight: 600;
}

.dark .prose th {
  background-color: #1f2937;
}

/* LeetCode-Style Modern UI Enhancements */

/* Professional Dark Theme Variables */
:root {
  --leetcode-bg-primary: #1a1a1a;
  --leetcode-bg-secondary: #262626;
  --leetcode-bg-tertiary: #2d2d2d;
  --leetcode-bg-elevated: #333333;
  --leetcode-border: #3a3a3a;
  --leetcode-border-light: #4a4a4a;
  --leetcode-text-primary: #ffffff;
  --leetcode-text-secondary: #cccccc;
  --leetcode-text-muted: #999999;
  --leetcode-accent: #ffa116;
  --leetcode-accent-hover: #ffb84d;
  --leetcode-success: #00af9b;
  --leetcode-success-bg: rgba(0, 175, 155, 0.1);
  --leetcode-error: #ef4743;
  --leetcode-error-bg: rgba(239, 71, 67, 0.1);
  --leetcode-warning: #ffc01e;
  --leetcode-warning-bg: rgba(255, 192, 30, 0.1);
  --leetcode-easy: #00b8a3;
  --leetcode-medium: #ffc01e;
  --leetcode-hard: #ef4743;
  --leetcode-shadow: rgba(0, 0, 0, 0.3);
  --leetcode-shadow-lg: rgba(0, 0, 0, 0.5);
}

/* Enhanced animations */
@keyframes leetcode-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes leetcode-slide-in {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes leetcode-fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes leetcode-scale-in {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes leetcode-glow {
  0%, 100% { box-shadow: 0 0 5px var(--leetcode-accent); }
  50% { box-shadow: 0 0 20px var(--leetcode-accent), 0 0 30px var(--leetcode-accent); }
}

/* Professional animations */
.leetcode-pulse { animation: leetcode-pulse 2s ease-in-out infinite; }
.leetcode-slide-in { animation: leetcode-slide-in 0.3s ease-out; }
.leetcode-fade-in { animation: leetcode-fade-in 0.2s ease-out; }
.leetcode-scale-in { animation: leetcode-scale-in 0.2s ease-out; }
.leetcode-glow { animation: leetcode-glow 2s ease-in-out infinite; }

/* Modern hover effects */
.leetcode-hover {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.leetcode-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px var(--leetcode-shadow);
}

.leetcode-button-hover {
  transition: all 0.15s ease;
  position: relative;
  overflow: hidden;
}

.leetcode-button-hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.leetcode-button-hover:hover::before {
  left: 100%;
}

.leetcode-button-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Professional card styles */
.leetcode-card {
  background: var(--leetcode-bg-secondary);
  border: 1px solid var(--leetcode-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.leetcode-card:hover {
  border-color: var(--leetcode-border-light);
  box-shadow: 0 4px 12px var(--leetcode-shadow);
}

.leetcode-card-elevated {
  background: var(--leetcode-bg-elevated);
  border: 1px solid var(--leetcode-border-light);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--leetcode-shadow);
}

/* Modern scrollbar */
.leetcode-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #4a4a4a #2d2d2d;
}

.leetcode-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.leetcode-scrollbar::-webkit-scrollbar-track {
  background: #2d2d2d;
  border-radius: 4px;
}

.leetcode-scrollbar::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 4px;
  border: 1px solid #3a3a3a;
}

.leetcode-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #5a5a5a;
}

/* Professional typography */
.leetcode-text-primary { color: var(--leetcode-text-primary); }
.leetcode-text-secondary { color: var(--leetcode-text-secondary); }
.leetcode-text-muted { color: var(--leetcode-text-muted); }

/* Status colors */
.leetcode-easy { color: var(--leetcode-easy); }
.leetcode-medium { color: var(--leetcode-medium); }
.leetcode-hard { color: var(--leetcode-hard); }
.leetcode-success { color: var(--leetcode-success); }
.leetcode-error { color: var(--leetcode-error); }
.leetcode-warning { color: var(--leetcode-warning); }

/* LeetCode-style backgrounds */
.leetcode-bg-primary { background-color: var(--leetcode-bg-primary); }
.leetcode-bg-secondary { background-color: var(--leetcode-bg-secondary); }
.leetcode-bg-tertiary { background-color: var(--leetcode-bg-tertiary); }
.leetcode-bg-elevated { background-color: var(--leetcode-bg-elevated); }

/* Professional borders */
.leetcode-border { border-color: var(--leetcode-border); }
.leetcode-border-light { border-color: var(--leetcode-border-light); }

/* Blockquotes in problem description */
.prose blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #6b7280;
}

.dark .prose blockquote {
  color: #9ca3af;
}

/* Headings in problem description */
.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  color: #111827;
}

.dark .prose h1, .dark .prose h2, .dark .prose h3, 
.dark .prose h4, .dark .prose h5, .dark .prose h6 {
  color: #f9fafb;
}

/* Links in problem description */
.prose a {
  color: #3b82f6;
  text-decoration: underline;
}

.prose a:hover {
  color: #1d4ed8;
}

.dark .prose a {
  color: #60a5fa;
}

.dark .prose a:hover {
  color: #93c5fd;
}
