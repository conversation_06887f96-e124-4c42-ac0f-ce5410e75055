import { Outfit } from 'next/font/google';
import './globals.css';
import Providers from '@/components/providers/Providers';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: {
    template: '%s | Mockly',
    default: 'Mockly - Interview Preparation Platform',
  },
  description: '<PERSON><PERSON><PERSON> helps you prepare for technical interviews with coding challenges, mock interviews, and more.',
};

const outfit = Outfit({
  subsets: ["latin"],
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={`${outfit.className} dark:bg-gray-900`}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
