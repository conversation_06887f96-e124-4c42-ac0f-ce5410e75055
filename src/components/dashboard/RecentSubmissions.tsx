'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';

interface CodingSubmission {
  _id: string;
  problemId: string;
  problemTitle: string;
  code: string;
  language: string;
  results: {
    status: 'Accepted' | 'Wrong Answer' | 'Time Limit Exceeded';
  };
  createdAt: string;
}

export default function RecentSubmissions() {
  const [submissions, setSubmissions] = useState<CodingSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const fetchSubmissions = async () => {
      try {
        const response = await fetch('/api/submissions/history');
        if (response.ok) {
          const data = await response.json();
          setSubmissions(data.slice(0, 5)); // Get only the 5 most recent submissions
        }
      } catch (error) {
        console.error('Error fetching submissions:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSubmissions();
  }, []);

  const handleSubmissionClick = (submissionId: string) => {
    router.push(`/coding/submission/${submissionId}`);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'M/d/yyyy, h:mm:ss a');
  };

  return (
    <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] md:p-6">
      <h3 className="mb-4 text-lg font-semibold text-gray-800 dark:text-white/90">
        Recent Coding Submissions
      </h3>
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-800">
              <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Problem</th>
              <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Status</th>
              <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Language</th>
              <th className="pb-3 text-left text-sm font-medium text-gray-500 dark:text-gray-400">Submitted</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan={4} className="py-4 text-center text-gray-500 dark:text-gray-400">
                  Loading submissions...
                </td>
              </tr>
            ) : submissions.length === 0 ? (
              <tr>
                <td colSpan={4} className="py-4 text-center text-gray-500 dark:text-gray-400">
                  No submissions found
                </td>
              </tr>
            ) : (
              submissions.map((submission) => (
                <tr 
                  key={submission._id} 
                  className="cursor-pointer border-b border-gray-200 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-white/[0.02]"
                  onClick={() => handleSubmissionClick(submission._id)}
                >
                  <td className="py-4">
                    <div>
                      <span className="text-sm font-medium text-gray-800 dark:text-white/90">
                        {submission.problemTitle}
                      </span>
                      <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                        {submission.problemId}
                      </span>
                    </div>
                  </td>
                  <td className="py-4">
                    <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                      submission.results.status === 'Accepted' 
                        ? 'bg-green-100 text-green-700 dark:bg-green-500/20 dark:text-green-400'
                        : submission.results.status === 'Wrong Answer'
                        ? 'bg-red-100 text-red-700 dark:bg-red-500/20 dark:text-red-400'
                        : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-500/20 dark:text-yellow-400'
                    }`}>
                      {submission.results.status}
                    </span>
                  </td>
                  <td className="py-4 text-sm text-gray-800 dark:text-white/90">
                    {submission.language}
                  </td>
                  <td className="py-4 text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(submission.createdAt)}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
