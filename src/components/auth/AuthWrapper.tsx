'use client';

import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import LoadingScreen from '../common/LoadingScreen';

interface AuthWrapperProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * A wrapper component that handles authentication state and loading
 * 
 * @param children - The content to render when authentication check is complete
 * @param requireAuth - Whether authentication is required (default: true)
 * @param redirectTo - Where to redirect if authentication fails (default: '/signin')
 */
const AuthWrapper: React.FC<AuthWrapperProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/signin',
}) => {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // If authentication check is complete
    if (status !== 'loading') {
      // If authentication is required but user is not authenticated
      if (requireAuth && !session) {
        router.push(redirectTo);
      } else {
        // Authentication check is complete and requirements are met
        // Set a small delay to ensure smooth transition
        const timer = setTimeout(() => {
          setIsLoading(false);
        }, 500);
        
        return () => clearTimeout(timer);
      }
    }
  }, [session, status, requireAuth, redirectTo, router]);

  // Show loading screen while checking authentication or during the transition
  if (isLoading || status === 'loading') {
    return <LoadingScreen />;
  }

  // Render children when authentication check is complete and requirements are met
  return <>{children}</>;
};

export default AuthWrapper;
