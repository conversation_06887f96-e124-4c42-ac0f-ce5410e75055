// Function to store questions in the database
async function storeQuestions(
  questions: Array<{text: string, type: string, tags?: string[]}>, 
  sessionId: string
) {
  if (!questions || !sessionId || questions.length === 0) {
    console.error('Missing or invalid questions/sessionId for storing questions');
    return;
  }
  
  // Ensure all required fields are present and properly formatted
  const questionsToStore = questions.map(q => ({
    sessionId: sessionId,
    text: q.text || 'Default question',
    type: q.type || 'technical',
    tags: q.tags || []
  }));
  
  try {
    console.log('Sending request to /api/questions/batch');
    const response = await fetch('/api/questions/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(questionsToStore),
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to store questions: ${errorText}`);
    }
    
    const result = await response.json();
    console.log('Questions stored successfully:', result);
    return result;
  } catch (error) {
    console.error('Error storing questions:', error);
    throw error;
  }
}
