'use client';

import { useState } from 'react';

export default function TestQuestionStore({ sessionId }: { sessionId: string }) {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setResult('');
    
    try {
      // Validate session ID
      if (!sessionId.trim()) {
        setError('Session ID is required');
        setLoading(false);
        return;
      }
      
      // Create sample questions
      const sampleQuestions = [
        {
          sessionId: sessionId,
          text: "Sample technical question?",
          type: "technical",
          tags: ["javascript", "react"]
        },
        {
          sessionId: sessionId,
          text: "Sample behavioral question?",
          type: "behavioral",
          tags: ["teamwork"]
        }
      ];
      
      console.log('Storing sample questions:', sampleQuestions);
      
      const response = await fetch('/api/questions/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sampleQuestions),
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to store questions: ${errorText}`);
      }
      
      const result = await response.json();
      setResult(JSON.stringify(result, null, 2));
    } catch (error) {
      setError(error instanceof Error ? error.message : String(error));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-white shadow-sm">
      <h3 className="text-lg font-semibold mb-4">Test Question Storage</h3>
      <p className="mb-2">Session ID: {sessionId}</p>
      <button
        onClick={handleSubmit}
        disabled={loading}
        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Test Store Questions'}
      </button>
      
      {error && (
        <div className="mt-4 p-3 bg-red-100 border border-red-300 text-red-800 rounded">
          <p className="font-semibold">Error:</p>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <div className="mt-4">
          <p className="font-semibold">Result:</p>
          <pre className="p-3 bg-gray-100 rounded overflow-auto max-h-60 text-sm">
            {result}
          </pre>
        </div>
      )}
    </div>
  );
}
