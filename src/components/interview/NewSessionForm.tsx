"use client";

import React, { useState } from 'react';
import Select from 'react-select';
import { useRouter } from 'next/navigation';

// Define CSS variables for light/dark mode
const GlobalStyles = () => (
  <style jsx global>{`
    :root {
      --bg-color: #ffffff;
      --text-color: #111827;
      --border-color: #E5E7EB;
      --placeholder-color: #6B7280;
      --dropdown-bg: #ffffff;
      --selected-bg: rgba(99, 102, 241, 0.1);
      --hover-bg: rgba(243, 244, 246, 0.8);
      --selected-text: #6366F1;
      --chip-bg: rgba(99, 102, 241, 0.1);
      --chip-text: #6366F1;
      --chip-remove: #6B7280;
      --chip-remove-hover-bg: rgba(99, 102, 241, 0.2);
      --chip-remove-hover-text: #4F46E5;
      --neutral5: #F3F4F6;
      --neutral10: #F3F4F6;
      --neutral20: #E5E7EB;
      --neutral30: #D1D5DB;
      --neutral40: #9CA3AF;
      --neutral50: #6B7280;
      --neutral60: #4B5563;
      --neutral70: #374151;
      --neutral80: #1F2937;
      --neutral90: #111827;
    }

    .dark {
      --bg-color: #1F2937;
      --text-color: #ffffff;
      --border-color: #374151;
      --placeholder-color: #9CA3AF;
      --dropdown-bg: #111827;
      --selected-bg: rgba(99, 102, 241, 0.2);
      --hover-bg: rgba(55, 65, 81, 0.7);
      --selected-text: #818CF8;
      --chip-bg: rgba(99, 102, 241, 0.2);
      --chip-text: #818CF8;
      --chip-remove: #9CA3AF;
      --chip-remove-hover-bg: rgba(99, 102, 241, 0.3);
      --chip-remove-hover-text: #C7D2FE;
      --neutral5: #374151;
      --neutral10: #374151;
      --neutral20: #4B5563;
      --neutral30: #6B7280;
      --neutral40: #9CA3AF;
      --neutral50: #D1D5DB;
      --neutral60: #E5E7EB;
      --neutral70: #F3F4F6;
      --neutral80: #F9FAFB;
      --neutral90: #FFFFFF;
    }
  `}</style>
);

// Define job profiles with more details
const popularJobs = [
  { 
    title: "Software Engineer", 
    icon: "💻",
    description: "Develop and maintain software applications",
    skills: ["JavaScript", "React", "Node.js", "TypeScript"],
    experienceLevel: "mid" 
  },
  { 
    title: "Data Scientist", 
    icon: "📊",
    description: "Analyze and interpret complex data",
    skills: ["Python", "SQL", "Machine Learning", "Statistics"],
    experienceLevel: "senior"
  },
  { 
    title: "Product Manager", 
    icon: "📱",
    description: "Lead product development and strategy",
    skills: ["Product Strategy", "User Research", "Agile", "Roadmapping"],
    experienceLevel: "senior"
  },
  { 
    title: "UX Designer", 
    icon: "🎨",
    description: "Create intuitive user experiences",
    skills: ["UI Design", "User Research", "Wireframing", "Prototyping"],
    experienceLevel: "mid"
  }
];

export default function NewSessionForm() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedJob, setSelectedJob] = useState("");
  const [jobTitle, setJobTitle] = useState("");
  const [experienceLevel, setExperienceLevel] = useState("");
  const [selectedSkills, setSelectedSkills] = useState<string[]>([]);
  const [behavioralAreas, setBehavioralAreas] = useState<string[]>([]);
  // const [includeVideo, setIncludeVideo] = useState(false);
  
  // Format skills for MultiSelect component
  const getSkillOptions = () => {
    const allSkills = [
      "JavaScript", "TypeScript", "React", "Angular", "Vue", "Node.js", 
      "Python", "Java", "C#", "SQL", "NoSQL", "AWS", "Azure", 
      "Docker", "Kubernetes", "CI/CD", "Git", "Machine Learning",
      "UI Design", "UX Research", "Product Strategy", "Agile", "Scrum"
    ];
    
    return allSkills.map(skill => ({
      value: skill.toLowerCase().replace(/\s+/g, '-'),
      label: skill
    }));
  };
  
  // Handle job selection
  const handleJobSelection = (job: typeof popularJobs[0]) => {
    setSelectedJob(job.title);
    setJobTitle(job.title);
    setExperienceLevel(job.experienceLevel);
    
    // Convert job skills to the format expected by MultiSelect
    const formattedSkills = job.skills.map(skill => 
      skill.toLowerCase().replace(/\s+/g, '-')
    );
    setSelectedSkills(formattedSkills);
  };
  
  // Remove a skill
  const removeSkill = (skillValue: string) => {
    setSelectedSkills(selectedSkills.filter(skill => skill !== skillValue));
  };
  
  // Experience level options
  const experienceLevelOptions = [
    { value: "entry", label: "Entry" },
    { value: "mid", label: "Mid" },
    { value: "senior", label: "Senior" }
  ];
  
  // Behavioral areas options
  const behavioralOptions = [
    { value: "leadership", label: "Leadership" },
    { value: "teamwork", label: "Teamwork" },
    { value: "communication", label: "Communication" },
    { value: "problem-solving", label: "Problem Solving" },
    { value: "adaptability", label: "Adaptability" }
  ];
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    if (!jobTitle || !experienceLevel || selectedSkills.length === 0) {
      setError("Please fill in all required fields");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      console.log('Generating interview questions for:', {
        jobTitle,
        experienceLevel,
        skills: selectedSkills,
        behavioralAreas
      });
      
      // Format skills and behavioral areas properly
      const formattedSkills = selectedSkills.map(skill => {
        const option = getSkillOptions().find(opt => opt.value === skill);
        return option ? option.label : skill;
      });
      
      const formattedBehavioralAreas = behavioralAreas.map(area => {
        const option = behavioralOptions.find(opt => opt.value === area);
        return option ? option.label : area;
      });
      
      // Generate questions using the Python API
      const technicalRequest = {
        job_profile: jobTitle,
        experience_level: experienceLevel.charAt(0).toUpperCase() + experienceLevel.slice(1),
        required_skills: formattedSkills,
        num_questions: 5 // Reduced from 10 to make it faster
      };
      
      const behavioralRequest = behavioralAreas.length > 0 ? {
        job_profile: jobTitle,
        experience_level: experienceLevel.charAt(0).toUpperCase() + experienceLevel.slice(1),
        focus_areas: formattedBehavioralAreas,
        num_questions: 3 // Reduced from 5 to make it faster
      } : null;
      
      console.log('Sending request to Python API:', { technicalRequest, behavioralRequest });
      
      // Create session in database first
      const sessionData = {
        userId: "000000000000000000000001", // Dummy user ID as placeholder
        role: jobTitle,
        roundType: "technical",
        duration: 30,
        language: "English",
        interviewer: "AI",
        termsAgreed: true,
        skills: formattedSkills,
        behavioralAreas: formattedBehavioralAreas || []
      };
      
      console.log('Creating session with data:', sessionData);
      
      const sessionResponse = await fetch('/api/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sessionData)
      });
      
      if (!sessionResponse.ok) {
        const errorText = await sessionResponse.text();
        console.error('Session creation error:', errorText);
        throw new Error(`Failed to create session: ${errorText}`);
      }
      
      const session = await sessionResponse.json();
      console.log('Session created:', session);
      
      // Now generate questions
      try {
        const questionsResponse = await fetch('http://localhost:8000/generate-interview-questions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            technical_request: technicalRequest,
            behavioral_request: behavioralRequest
          })
        });
        
        if (!questionsResponse.ok) {
          const errorText = await questionsResponse.text();
          console.error('Questions generation error:', errorText);
          // Continue with session even if questions fail
          router.push(`/interview/session/${session._id}`);
          return;
        }
        
        const generatedQuestions = await questionsResponse.json();
        console.log('Questions generated successfully:', generatedQuestions);
        
        // Store questions in database
        if (generatedQuestions.technical_questions?.length > 0 || 
            generatedQuestions.behavioral_questions?.length > 0) {
          
          const questionsToStore = [
            ...(generatedQuestions.technical_questions || []).map((q: string) => ({
              sessionId: session._id,
              text: q,
              type: 'technical',
              tags: formattedSkills
            })),
            ...(generatedQuestions.behavioral_questions || []).map((q: string) => ({
              sessionId: session._id,
              text: q,
              type: 'behavioral',
              tags: formattedBehavioralAreas
            }))
          ];
          
          console.log('Storing questions in database:', questionsToStore);
          
          try {
            // Log the exact data being sent to the API
            console.log('Sending questions to API:', JSON.stringify(questionsToStore));
            
            const questionsStoreResponse = await fetch('/api/questions/batch', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(questionsToStore)
            });
            
            console.log('Response status:', questionsStoreResponse.status);
            
            if (!questionsStoreResponse.ok) {
              const errorText = await questionsStoreResponse.text();
              console.error('Failed to store questions:', errorText);
              // Try to parse as JSON if possible
              try {
                const errorData = JSON.parse(errorText);
                console.error('Error details:', errorData);
              } catch (e) {
                // Not JSON, just log the text
                console.error('Raw error response:', errorText);
              }
            } else {
              const storedQuestions = await questionsStoreResponse.json();
              console.log('Questions stored successfully:', storedQuestions);
            }
          } catch (err) {
            console.error('Error storing questions:', err);
            console.error('Error name:', err instanceof Error ? err.name : 'Unknown');
            console.error('Error message:', err instanceof Error ? err.message : String(err));
            if (err instanceof Error && err.stack) {
              console.error('Error stack:', err.stack);
            }
          }
        }
      } catch (err) {
        console.error('Error generating questions:', err);
        // Continue with session even if questions fail
      }
      
      // Redirect to the interview page with the session ID
      router.push(`/interview/device-check/${session._id}`);
    } catch (err) {
      console.error('Error in form submission:', err);
      setError(err instanceof Error ? err.message : 'Failed to process your request. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <>
      <GlobalStyles />
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Section - Popular Jobs */}
        <div>
          <div className="p-6 h-full bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:via-gray-900/95 dark:to-gray-800/90 backdrop-blur-xl rounded-xl border border-gray-200 dark:border-gray-700/50 shadow-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Popular Job Profiles
            </h3>
            <div className="grid grid-cols-1 gap-4">
              {popularJobs.map((job) => (
                <button
                  key={job.title}
                  onClick={() => handleJobSelection(job)}
                  className={`flex flex-col p-4 rounded-xl transition-all duration-200 ${
                    selectedJob === job.title
                      ? "bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-blue-500/20 dark:to-indigo-500/20 border-indigo-200 dark:border-indigo-400/30 border shadow-md"
                      : "bg-gray-50 hover:bg-gray-100 dark:bg-gray-800/40 dark:hover:bg-gray-800/60 border border-gray-200 dark:border-gray-700/50 hover:border-gray-300 dark:hover:border-gray-600/50"
                  }`}
                >
                  <div className="flex items-center mb-2">
                    <span className="text-2xl mr-3">{job.icon}</span>
                    <span className={`font-medium text-gray-900 dark:text-white`}>
                      {job.title}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{job.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {job.skills.slice(0, 3).map(skill => (
                      <span key={skill} className="text-xs px-2.5 py-1 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300 backdrop-blur-sm border border-gray-200 dark:border-gray-600/30">
                        {skill}
                      </span>
                    ))}
                    {job.skills.length > 3 && (
                      <span className="text-xs px-2.5 py-1 rounded-full bg-gray-100 dark:bg-gray-700/50 text-gray-700 dark:text-gray-300 backdrop-blur-sm border border-gray-200 dark:border-gray-600/30">
                        +{job.skills.length - 3}
                      </span>
                    )}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>
        
        {/* Right Section - Form */}
        <div>
          <div className="p-6 h-full bg-white dark:bg-gradient-to-br dark:from-gray-900 dark:via-gray-900/95 dark:to-gray-800/90 backdrop-blur-xl rounded-xl border border-gray-200 dark:border-gray-700/50 shadow-lg">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">
              Interview Configuration
            </h3>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Job Profile <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <Select 
                  value={jobTitle ? { value: jobTitle, label: jobTitle } : null}
                  onChange={(option) => setJobTitle(option?.value || "")}
                  options={popularJobs.map(job => ({ value: job.title, label: job.title }))}
                  placeholder="Select job profile"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  styles={{
                    control: (base, state) => ({
                      ...base,
                      background: 'var(--bg-color, #fff)',
                      borderColor: state.isFocused ? '#6366F1' : 'var(--border-color, #E5E7EB)',
                      boxShadow: state.isFocused ? '0 0 0 1px #6366F1' : 'none',
                      '&:hover': {
                        borderColor: '#6366F1'
                      }
                    }),
                    input: (base) => ({
                      ...base,
                      color: 'var(--text-color, #111827)'
                    }),
                    singleValue: (base) => ({
                      ...base,
                      color: 'var(--text-color, #111827)'
                    }),
                    placeholder: (base) => ({
                      ...base,
                      color: 'var(--placeholder-color, #6B7280)'
                    }),
                    menu: (base) => ({
                      ...base,
                      background: 'var(--dropdown-bg, #fff)',
                      backdropFilter: 'blur(8px)',
                      border: '1px solid var(--border-color, #E5E7EB)',
                      borderRadius: '0.75rem',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }),
                    option: (base, state) => ({
                      ...base,
                      backgroundColor: state.isSelected 
                        ? 'var(--selected-bg, rgba(99, 102, 241, 0.1))'
                        : state.isFocused
                        ? 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                        : 'transparent',
                      color: state.isSelected 
                        ? 'var(--selected-text, #6366F1)'
                        : 'var(--text-color, #111827)',
                      '&:hover': {
                        backgroundColor: 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                      }
                    }),
                    multiValue: (base) => ({
                      ...base,
                      backgroundColor: 'var(--chip-bg, rgba(99, 102, 241, 0.1))',
                      borderRadius: '0.5rem'
                    }),
                    multiValueLabel: (base) => ({
                      ...base,
                      color: 'var(--chip-text, #6366F1)',
                      padding: '2px'
                    }),
                    multiValueRemove: (base) => ({
                      ...base,
                      color: 'var(--chip-remove, #6B7280)',
                      ':hover': {
                        backgroundColor: 'var(--chip-remove-hover-bg, rgba(99, 102, 241, 0.2))',
                        color: 'var(--chip-remove-hover-text, #4F46E5)'
                      }
                    })
                  }}
                  theme={(theme) => ({
                    ...theme,
                    colors: {
                      ...theme.colors,
                      primary: '#6366F1',
                      primary75: '#818CF8',
                      primary50: '#EEF2FF',
                      primary25: '#F5F3FF',
                      neutral0: 'var(--bg-color, #fff)',
                      neutral5: 'var(--neutral5, #F3F4F6)',
                      neutral10: 'var(--neutral10, #F3F4F6)',
                      neutral20: 'var(--neutral20, #E5E7EB)',
                      neutral30: 'var(--neutral30, #D1D5DB)',
                      neutral40: 'var(--neutral40, #9CA3AF)',
                      neutral50: 'var(--neutral50, #6B7280)',
                      neutral60: 'var(--neutral60, #4B5563)',
                      neutral70: 'var(--neutral70, #374151)',
                      neutral80: 'var(--neutral80, #1F2937)',
                      neutral90: 'var(--neutral90, #111827)'
                    }
                  })}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Experience Level <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <Select 
                  value={experienceLevelOptions.find(option => option.value === experienceLevel) || null}
                  onChange={(option) => setExperienceLevel(option?.value || "")}
                  options={experienceLevelOptions}
                  placeholder="Select experience level"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  styles={{
                    control: (base, state) => ({
                      ...base,
                      background: 'var(--bg-color, #fff)',
                      borderColor: state.isFocused ? '#6366F1' : 'var(--border-color, #E5E7EB)',
                      boxShadow: state.isFocused ? '0 0 0 1px #6366F1' : 'none',
                      '&:hover': {
                        borderColor: '#6366F1'
                      }
                    }),
                    input: (base) => ({
                      ...base,
                      color: 'var(--text-color, #111827)'
                    }),
                    singleValue: (base) => ({
                      ...base,
                      color: 'var(--text-color, #111827)'
                    }),
                    placeholder: (base) => ({
                      ...base,
                      color: 'var(--placeholder-color, #6B7280)'
                    }),
                    menu: (base) => ({
                      ...base,
                      background: 'var(--dropdown-bg, #fff)',
                      backdropFilter: 'blur(8px)',
                      border: '1px solid var(--border-color, #E5E7EB)',
                      borderRadius: '0.75rem',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }),
                    option: (base, state) => ({
                      ...base,
                      backgroundColor: state.isSelected 
                        ? 'var(--selected-bg, rgba(99, 102, 241, 0.1))'
                        : state.isFocused
                        ? 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                        : 'transparent',
                      color: state.isSelected 
                        ? 'var(--selected-text, #6366F1)'
                        : 'var(--text-color, #111827)',
                      '&:hover': {
                        backgroundColor: 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                      }
                    })
                  }}
                  theme={(theme) => ({
                    ...theme,
                    colors: {
                      ...theme.colors,
                      primary: '#6366F1',
                      primary75: '#818CF8',
                      primary50: '#EEF2FF',
                      primary25: '#F5F3FF',
                      neutral0: 'var(--bg-color, #fff)',
                      neutral5: 'var(--neutral5, #F3F4F6)',
                      neutral10: 'var(--neutral10, #F3F4F6)',
                      neutral20: 'var(--neutral20, #E5E7EB)',
                      neutral30: 'var(--neutral30, #D1D5DB)',
                      neutral40: 'var(--neutral40, #9CA3AF)',
                      neutral50: 'var(--neutral50, #6B7280)',
                      neutral60: 'var(--neutral60, #4B5563)',
                      neutral70: 'var(--neutral70, #374151)',
                      neutral80: 'var(--neutral80, #1F2937)',
                      neutral90: 'var(--neutral90, #111827)'
                    }
                  })}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Required Skills <span className="text-red-500 dark:text-red-400">*</span>
                </label>
                <Select 
                  isMulti
                  value={selectedSkills.map(skill => {
                    const option = getSkillOptions().find(opt => opt.value === skill);
                    return option || { value: skill, label: skill };
                  })}
                  onChange={(options) => {
                    setSelectedSkills(options.map(option => option.value));
                  }}
                  options={getSkillOptions()}
                  placeholder="Select required skills"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  styles={{
                    control: (base, state) => ({
                      ...base,
                      background: 'var(--bg-color, #fff)',
                      borderColor: state.isFocused ? '#6366F1' : 'var(--border-color, #E5E7EB)',
                      boxShadow: state.isFocused ? '0 0 0 1px #6366F1' : 'none',
                      '&:hover': {
                        borderColor: '#6366F1'
                      }
                    }),
                    input: (base) => ({
                      ...base,
                      color: 'var(--text-color, #111827)'
                    }),
                    placeholder: (base) => ({
                      ...base,
                      color: 'var(--placeholder-color, #6B7280)'
                    }),
                    menu: (base) => ({
                      ...base,
                      background: 'var(--dropdown-bg, #fff)',
                      backdropFilter: 'blur(8px)',
                      border: '1px solid var(--border-color, #E5E7EB)',
                      borderRadius: '0.75rem',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }),
                    option: (base, state) => ({
                      ...base,
                      backgroundColor: state.isSelected 
                        ? 'var(--selected-bg, rgba(99, 102, 241, 0.1))'
                        : state.isFocused
                        ? 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                        : 'transparent',
                      color: state.isSelected 
                        ? 'var(--selected-text, #6366F1)'
                        : 'var(--text-color, #111827)',
                      '&:hover': {
                        backgroundColor: 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                      }
                    }),
                    multiValue: (base) => ({
                      ...base,
                      backgroundColor: 'var(--chip-bg, rgba(99, 102, 241, 0.1))',
                      borderRadius: '0.5rem'
                    }),
                    multiValueLabel: (base) => ({
                      ...base,
                      color: 'var(--chip-text, #6366F1)',
                      padding: '2px'
                    }),
                    multiValueRemove: (base) => ({
                      ...base,
                      color: 'var(--chip-remove, #6B7280)',
                      ':hover': {
                        backgroundColor: 'var(--chip-remove-hover-bg, rgba(99, 102, 241, 0.2))',
                        color: 'var(--chip-remove-hover-text, #4F46E5)'
                      }
                    })
                  }}
                  theme={(theme) => ({
                    ...theme,
                    colors: {
                      ...theme.colors,
                      primary: '#6366F1',
                      primary75: '#818CF8',
                      primary50: '#EEF2FF',
                      primary25: '#F5F3FF',
                      neutral0: 'var(--bg-color, #fff)',
                      neutral5: 'var(--neutral5, #F3F4F6)',
                      neutral10: 'var(--neutral10, #F3F4F6)',
                      neutral20: 'var(--neutral20, #E5E7EB)',
                      neutral30: 'var(--neutral30, #D1D5DB)',
                      neutral40: 'var(--neutral40, #9CA3AF)',
                      neutral50: 'var(--neutral50, #6B7280)',
                      neutral60: 'var(--neutral60, #4B5563)',
                      neutral70: 'var(--neutral70, #374151)',
                      neutral80: 'var(--neutral80, #1F2937)',
                      neutral90: 'var(--neutral90, #111827)'
                    }
                  })}
                />
                <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Select at least 3 skills relevant to the position</p>
                
                {/* Selected Skills Display */}
                {selectedSkills.length > 0 && (
                  <div className="flex flex-wrap gap-2 mt-3">
                    {selectedSkills.map(skill => {
                      const skillLabel = getSkillOptions().find(opt => opt.value === skill)?.label || skill;
                      return (
                        <div key={skill} 
                          className="flex items-center bg-indigo-500/20 text-indigo-200 px-3 py-1.5 rounded-full border border-indigo-500/30"
                        >
                          <span>{skillLabel}</span>
                          <button 
                            onClick={() => removeSkill(skill)}
                            className="ml-2 text-indigo-300 hover:text-indigo-100 transition-colors"
                          >
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </button>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
                  Behavioral Focus Areas <span className="text-gray-500 text-sm dark:text-gray-500">(Optional)</span>
                </label>
                <Select 
                  isMulti
                  value={behavioralAreas.map(area => {
                    const option = behavioralOptions.find(opt => opt.value === area);
                    return option || { value: area, label: area };
                  })}
                  onChange={(options) => {
                    setBehavioralAreas(options.map(option => option.value));
                  }}
                  options={behavioralOptions}
                  placeholder="Select behavioral areas"
                  className="react-select-container"
                  classNamePrefix="react-select"
                  styles={{
                    control: (base, state) => ({
                      ...base,
                      background: 'var(--bg-color, #fff)',
                      borderColor: state.isFocused ? '#6366F1' : 'var(--border-color, #E5E7EB)',
                      boxShadow: state.isFocused ? '0 0 0 1px #6366F1' : 'none',
                      '&:hover': {
                        borderColor: '#6366F1'
                      }
                    }),
                    input: (base) => ({
                      ...base,
                      color: 'var(--text-color, #111827)'
                    }),
                    placeholder: (base) => ({
                      ...base,
                      color: 'var(--placeholder-color, #6B7280)'
                    }),
                    menu: (base) => ({
                      ...base,
                      background: 'var(--dropdown-bg, #fff)',
                      backdropFilter: 'blur(8px)',
                      border: '1px solid var(--border-color, #E5E7EB)',
                      borderRadius: '0.75rem',
                      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }),
                    option: (base, state) => ({
                      ...base,
                      backgroundColor: state.isSelected 
                        ? 'var(--selected-bg, rgba(99, 102, 241, 0.1))'
                        : state.isFocused
                        ? 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                        : 'transparent',
                      color: state.isSelected 
                        ? 'var(--selected-text, #6366F1)'
                        : 'var(--text-color, #111827)',
                      '&:hover': {
                        backgroundColor: 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                      }
                    }),
                    multiValue: (base) => ({
                      ...base,
                      backgroundColor: 'var(--chip-bg, rgba(99, 102, 241, 0.1))',
                      borderRadius: '0.5rem'
                    }),
                    multiValueLabel: (base) => ({
                      ...base,
                      color: 'var(--chip-text, #6366F1)',
                      padding: '2px'
                    }),
                    multiValueRemove: (base) => ({
                      ...base,
                      color: 'var(--chip-remove, #6B7280)',
                      ':hover': {
                        backgroundColor: 'var(--chip-remove-hover-bg, rgba(99, 102, 241, 0.2))',
                        color: 'var(--chip-remove-hover-text, #4F46E5)'
                      }
                    })
                  }}
                  theme={(theme) => ({
                    ...theme,
                    colors: {
                      ...theme.colors,
                      primary: '#6366F1',
                      primary75: '#818CF8',
                      primary50: '#EEF2FF',
                      primary25: '#F5F3FF',
                      neutral0: 'var(--bg-color, #fff)',
                      neutral5: 'var(--neutral5, #F3F4F6)',
                      neutral10: 'var(--neutral10, #F3F4F6)',
                      neutral20: 'var(--neutral20, #E5E7EB)',
                      neutral30: 'var(--neutral30, #D1D5DB)',
                      neutral40: 'var(--neutral40, #9CA3AF)',
                      neutral50: 'var(--neutral50, #6B7280)',
                      neutral60: 'var(--neutral60, #4B5563)',
                      neutral70: 'var(--neutral70, #374151)',
                      neutral80: 'var(--neutral80, #1F2937)',
                      neutral90: 'var(--neutral90, #111827)'
                    }
                  })}
                />
              </div>
              
              {error && (
                <div className="p-3 bg-red-100 border border-red-200 text-red-700 rounded-lg text-sm">
                  {error}
                </div>
              )}
              
              <div className="pt-4">
                <button 
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors duration-200 shadow-lg shadow-indigo-500/20 disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Generating...' : 'Generate Interview Questions'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </>
  );
}
