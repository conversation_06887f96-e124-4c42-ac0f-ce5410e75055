import React, { useState } from "react";

interface Option {
  value: string;
  label: string;
}

interface SelectProps {
  options: Option[];
  placeholder?: string;
  onChange: (value: string) => void;
  className?: string;
  defaultValue?: string;
  value?: string;
  error?: string | boolean;
}

const Select: React.FC<SelectProps> = ({
  options,
  placeholder = "Select an option",
  onChange,
  className = "",
  defaultValue = "",
  value,
  error = false,
}) => {
  // Manage the selected value
  const [selectedValue, setSelectedValue] = useState<string>(value || defaultValue);

  // Update internal state when value prop changes
  React.useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newValue = e.target.value;
    setSelectedValue(newValue);
    onChange(newValue); // Trigger parent handler
  };

  // Determine border color based on error state
  const borderClass = error
    ? "border-error-500 focus:border-error-300 focus:ring-error-500/10 dark:border-error-700"
    : "border-gray-300 focus:border-brand-300 focus:ring-brand-500/10 dark:border-gray-700";

  return (
    <div className="relative">
      <select
        className={`h-11 w-full appearance-none rounded-lg border ${borderClass} px-4 py-2.5 pr-11 text-sm shadow-theme-xs placeholder:text-gray-400 focus:outline-hidden focus:ring-3 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800 ${
          selectedValue
            ? "text-gray-800 dark:text-white/90"
            : "text-gray-400 dark:text-gray-400"
        } ${className}`}
        value={selectedValue}
        onChange={handleChange}
      >
      {/* Placeholder option */}
      <option
        value=""
        disabled
        className="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
      >
        {placeholder}
      </option>
      {/* Map over options */}
      {options.map((option) => (
        <option
          key={option.value}
          value={option.value}
          className="text-gray-700 dark:bg-gray-900 dark:text-gray-400"
        >
          {option.label}
        </option>
      ))}
    </select>
      {typeof error === 'string' && error && (
        <p className="mt-1 text-xs text-error-500">{error}</p>
      )}
    </div>
  );
};

export default Select;
