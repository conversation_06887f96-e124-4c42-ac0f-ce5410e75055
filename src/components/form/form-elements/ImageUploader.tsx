"use client";

import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { PlusIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { toast } from "react-hot-toast";
import Image from "next/image";

interface ImageUploaderProps {
  onImageUpload: (imageUrl: string) => void;
  onError?: (error: string) => void;
  maxFiles?: number;
  folder?: string;
  className?: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUpload,
  onError,
  maxFiles = 1,
  folder = "uploads",
  className = "",
}) => {
  const [uploading, setUploading] = useState(false);
  const [preview, setPreview] = useState<string | null>(null);

  const onDrop = async (acceptedFiles: File[]) => {
    if (acceptedFiles.length === 0) return;

    // Only use the first file if multiple files are dropped
    const file = acceptedFiles[0];
    
    // Create a preview
    setPreview(URL.createObjectURL(file));
    
    // Upload the file
    await uploadImage(file);
  };

  const uploadImage = async (file: File) => {
    setUploading(true);
    
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", folder);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to upload image");
      }

      const data = await response.json();
      
      // Call the callback with the image URL
      onImageUpload(data.url);
      toast.success("Image uploaded successfully");
    } catch (error) {
      console.error("Error uploading image:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to upload image";
      toast.error(errorMessage);
      if (onError) onError(errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/png": [],
      "image/jpeg": [],
      "image/webp": [],
      "image/gif": [],
    },
    maxFiles,
  });

  const removePreview = () => {
    setPreview(null);
  };

  return (
    <div className={className}>
      {!preview ? (
        <div
          {...getRootProps()}
          className={`transition border border-gray-300 border-dashed cursor-pointer dark:hover:border-brand-500 dark:border-gray-700 rounded-xl hover:border-brand-500 ${
            isDragActive
              ? "border-brand-500 bg-gray-100 dark:bg-gray-800"
              : "border-gray-300 bg-gray-50 dark:border-gray-700 dark:bg-gray-900"
          } ${uploading ? "opacity-50 pointer-events-none" : ""}`}
        >
          <div className="flex flex-col items-center justify-center p-6 text-center">
            <input {...getInputProps()} />
            <div className="mb-3 rounded-full bg-brand-50 p-3 dark:bg-brand-900/20">
              <PlusIcon className="h-6 w-6 text-brand-500" />
            </div>
            <p className="mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
              {isDragActive ? "Drop the image here" : "Drag & drop an image here"}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              or click to browse (PNG, JPG, WebP, GIF)
            </p>
            {uploading && (
              <div className="mt-3">
                <div className="h-5 w-5 animate-spin rounded-full border-2 border-gray-300 border-t-brand-500"></div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="relative rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700">
          <div className="aspect-w-16 aspect-h-9 relative">
            <Image
              src={preview}
              alt="Preview"
              fill
              className="object-cover"
            />
          </div>
          <button
            type="button"
            onClick={removePreview}
            className="absolute top-2 right-2 rounded-full bg-gray-800/70 p-1 text-white hover:bg-gray-900/90"
          >
            <XMarkIcon className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default ImageUploader;
