"use client";
import React from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";

interface UserInfoProps {
  userData?: any;
}

export default function UserInfoCard({ userData }: UserInfoProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const handleSave = () => {
    // Handle save logic here
    console.log("Saving changes...");
    closeModal();
  };
  return (
    <div className="p-5 border border-gray-700/20 rounded-2xl bg-white shadow-sm dark:border-gray-700/30 dark:bg-gray-900 lg:p-6">
      <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div className="w-full">
          <div className="flex items-center justify-between mb-5">
            <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 flex items-center">
              <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
              Personal Information
            </h4>
            <button
              onClick={openModal}
              className="inline-flex items-center gap-2 text-sm font-medium text-brand-500 transition-colors hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 bg-white/80 dark:bg-gray-800/50 rounded-full px-3 py-1 shadow-sm"
            >
              <svg
                stroke="currentColor"
                fill="none"
                strokeWidth="2"
                viewBox="0 0 24 24"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="h-[14px] w-[14px]"
                height="1em"
                width="1em"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
              </svg>
              Edit
            </button>
          </div>

          <div className="p-5 bg-transparent dark:bg-transparent rounded-xl">
            <div className="grid grid-cols-1 gap-5 md:grid-cols-2 lg:gap-6">
            <div className="p-3 border border-gray-700/30 rounded-md bg-transparent dark:bg-transparent">
              <div className="flex items-center mb-2">
                <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <p className="text-xs font-medium uppercase text-gray-500">
                  First Name
                </p>
              </div>
              <p className="text-sm font-medium text-gray-800">
                {userData?.firstName || userData?.name?.split(' ')[0] || "Denis"}
              </p>
            </div>

            <div className="p-3 border border-gray-700/30 rounded-md bg-transparent dark:bg-transparent">
              <div className="flex items-center mb-2">
                <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
                <p className="text-xs font-medium uppercase text-gray-500">
                  Last Name
                </p>
              </div>
              <p className="text-sm font-medium text-gray-800">
                {userData?.lastName || userData?.name?.split(' ').slice(1).join(' ') || "Ruparel"}
              </p>
            </div>

            <div className="p-3 border border-gray-700/30 rounded-md bg-transparent dark:bg-transparent">
              <div className="flex items-center mb-2">
                <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                <p className="text-xs font-medium uppercase text-gray-500">
                  Email Address
                </p>
              </div>
              <p className="text-sm font-medium text-gray-800 break-all">
                {userData?.email || "<EMAIL>"}
              </p>
            </div>

            <div className="p-3 border border-gray-700/30 rounded-md bg-transparent dark:bg-transparent">
              <div className="flex items-center mb-2">
                <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                </svg>
                <p className="text-xs font-medium uppercase text-gray-500">
                  Phone
                </p>
              </div>
              <p className="text-sm font-medium text-gray-800">
                {userData?.phone || "+09 363 398 46"}
              </p>
            </div>
            </div>
          </div>
        </div>
      </div>

      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[550px] m-4">
        <div className="relative w-full overflow-y-auto bg-white no-scrollbar rounded-3xl dark:bg-gray-900 lg:p-8 p-4">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-xl font-semibold text-gray-800 dark:text-white/90">
              Edit Personal Information
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400">
              Update your contact details
            </p>
          </div>
          <form className="flex flex-col">
            <div className="px-2">
              <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                <div className="col-span-2 lg:col-span-1">
                  <Label>
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      First Name
                    </span>
                  </Label>
                  <Input
                    type="text"
                    defaultValue={userData?.firstName || userData?.name?.split(' ')[0] || "Denis"}
                  />
                </div>

                <div className="col-span-2 lg:col-span-1">
                  <Label>
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      Last Name
                    </span>
                  </Label>
                  <Input
                    type="text"
                    defaultValue={userData?.lastName || userData?.name?.split(' ').slice(1).join(' ') || "Ruparel"}
                  />
                </div>

                <div className="col-span-2 lg:col-span-1">
                  <Label>
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                      </svg>
                      Email Address
                    </span>
                  </Label>
                  <Input
                    type="email"
                    defaultValue={userData?.email || "<EMAIL>"}
                  />
                </div>

                <div className="col-span-2 lg:col-span-1">
                  <Label>
                    <span className="flex items-center">
                      <svg className="w-4 h-4 mr-2 text-blue-500/70" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                      </svg>
                      Phone
                    </span>
                  </Label>
                  <Input
                    type="tel"
                    defaultValue={userData?.phone || "+09 363 398 46"}
                  />
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end gap-4 px-2 mt-8">
              <Button
                variant="outline"
                onClick={closeModal}
                className="w-full lg:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="w-full lg:w-auto"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
