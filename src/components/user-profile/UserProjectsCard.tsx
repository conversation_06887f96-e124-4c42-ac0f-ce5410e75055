"use client";
import React, { useState } from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import TextArea from "../form/input/TextArea";
import DatePicker from "../form/date-picker";

interface UserProjectsProps {
  userData?: any;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function UserProjectsCard({ userData }: UserProjectsProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const [projects, setProjects] = useState([
    {
      title: "E-commerce Platform",
      description: "Built a full-stack e-commerce platform with React, Node.js, and MongoDB.",
      technologies: ["React", "Node.js", "MongoDB", "Express"],
      url: "https://github.com/example/ecommerce",
      startDate: "2021-06-01",
      endDate: "2022-01-15"
    }
  ]);

  interface Project {
    title: string;
    description: string;
    technologies: string[];
    url: string;
    startDate: string;
    endDate: string;
  }

  const [newProject, setNewProject] = useState<Project>({
    title: "",
    description: "",
    technologies: [],
    url: "",
    startDate: "",
    endDate: ""
  });

  const [techInput, setTechInput] = useState("");
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isOngoing, setIsOngoing] = useState<boolean>(false);

  const handleAddTech = () => {
    if (techInput && !newProject.technologies.includes(techInput)) {
      setNewProject({
        ...newProject,
        technologies: [...newProject.technologies, techInput]
      });
      setTechInput("");
    }
  };

  const handleRemoveTech = (tech: string) => {
    setNewProject({
      ...newProject,
      technologies: newProject.technologies.filter(t => t !== tech)
    });
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!newProject.title.trim()) {
      newErrors.title = "Project title is required";
    }

    if (!newProject.description.trim()) {
      newErrors.description = "Project description is required";
    }

    if (!newProject.startDate) {
      newErrors.startDate = "Start date is required";
    }

    if (!isOngoing && !newProject.endDate) {
      newErrors.endDate = "End date is required if not ongoing";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddProject = (e?: React.MouseEvent) => {
    if (e) e.preventDefault();

    if (validateForm()) {
      // Create a copy of the project object
      const projectToAdd = { ...newProject };

      // If ongoing project, set endDate to empty
      if (isOngoing) {
        projectToAdd.endDate = "";
      }

      setProjects([...projects, projectToAdd]);
      setNewProject({
        title: "",
        description: "",
        technologies: [],
        url: "",
        startDate: "",
        endDate: ""
      });
      setIsOngoing(false);
      setErrors({});
    }
  };

  const handleOngoingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsOngoing(e.target.checked);
    if (e.target.checked) {
      // Clear end date if ongoing
      setNewProject({...newProject, endDate: ""});
      // Clear any end date error
      setErrors({...errors, endDate: ""});
    }
  };

  const handleRemoveProject = (index: number) => {
    const updatedProjects = [...projects];
    updatedProjects.splice(index, 1);
    setProjects(updatedProjects);
  };

  const handleSave = () => {
    // Handle save logic here
    console.log("Saving project changes...", projects);
    closeModal();
  };

  return (
    <>
      <div className="p-5 border border-gray-200 rounded-2xl bg-gradient-to-b from-gray-50 to-white shadow-sm dark:border-gray-800 dark:from-gray-900/50 dark:to-gray-900 lg:p-6">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div className="w-full">
            <div className="flex items-center justify-between mb-5">
              <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 flex items-center">
                <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
                Projects
              </h4>
              <button
                onClick={openModal}
                className="inline-flex items-center gap-2 text-sm font-medium text-brand-500 transition-colors hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 bg-white/80 dark:bg-gray-800/50 rounded-full px-3 py-1 shadow-sm"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-[14px] w-[14px]"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                Edit
              </button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              {projects.map((project, index) => (
                <div key={index} className="p-5 border border-gray-100 rounded-xl bg-white dark:bg-gray-800/50 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col h-full">
                  <div className="flex-1">
                    <div className="flex justify-between items-start mb-2">
                      <h5 className="font-semibold text-gray-800 dark:text-white/90 text-lg">{project.title}</h5>
                      <div className="flex items-center space-x-1">
                        <span className="px-2.5 py-0.5 bg-brand-50 text-brand-600 text-xs font-medium rounded-full dark:bg-brand-900/30 dark:text-brand-400">
                          {project.endDate ? 'Completed' : 'In Progress'}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">{project.description}</p>
                    <div className="flex flex-wrap gap-1.5 mb-3">
                      {project.technologies.map((tech, techIndex) => (
                        <span key={techIndex} className="px-2.5 py-1 text-xs bg-gray-100 text-gray-700 font-medium rounded-full dark:bg-gray-700/70 dark:text-gray-300">
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                    <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                      <svg className="w-3.5 h-3.5 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      {new Date(project.startDate).toLocaleDateString()} - {project.endDate ? new Date(project.endDate).toLocaleDateString() : 'Present'}
                    </p>
                    {project.url && (
                      <a
                        href={project.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-xs font-medium text-brand-500 hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300"
                      >
                        View Project
                        <svg className="w-3.5 h-3.5 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                      </a>
                    )}
                  </div>
                </div>
              ))}
              {projects.length === 0 && (
                <div className="py-10 px-6 border border-dashed border-gray-200 rounded-xl dark:border-gray-700 flex flex-col items-center justify-center md:col-span-2">
                  <svg className="w-10 h-10 text-gray-300 dark:text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">No projects added yet.</p>
                  <button
                    onClick={openModal}
                    className="inline-flex items-center gap-1.5 rounded-md bg-indigo-500 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900 mt-3"
                  >
                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Project
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
        <div className="relative w-full p-4 overflow-y-auto bg-white no-scrollbar rounded-3xl dark:bg-gray-900 lg:p-11">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
              {projects.length > 0 ? 'Edit Projects' : 'Add Projects'}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {projects.length > 0
                ? 'Update your projects to showcase your skills and achievements.'
                : 'Add your projects to showcase your skills and achievements.'}
            </p>
          </div>
          <form className="flex flex-col">
            <div className="px-2 overflow-y-auto custom-scrollbar h-[450px]">
              <div className="mb-6">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                  </svg>
                  Your Projects
                </h5>
                {projects.length === 0 ? (
                  <div className="p-6 border border-dashed border-gray-200 rounded-xl dark:border-gray-700 flex flex-col items-center justify-center mb-4">
                    <svg className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <p className="text-gray-500 dark:text-gray-400">No projects added yet.</p>
                  </div>
                ) : (
                  projects.map((project, index) => (
                    <div key={index} className="p-5 mb-4 border border-gray-100 rounded-xl bg-white dark:bg-gray-800/50 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                      <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h6 className="font-semibold text-gray-800 dark:text-white/90 text-lg">{project.title}</h6>
                              {project.url && (
                                <a
                                  href={project.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-brand-500 dark:text-brand-400 font-medium flex items-center text-sm hover:underline"
                                >
                                  <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                  </svg>
                                  View Project
                                </a>
                              )}
                            </div>
                            <div className="flex items-center">
                              <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${
                                project.endDate ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              }`}>
                                {project.endDate ? 'Completed' : 'In Progress'}
                              </span>
                            </div>
                          </div>

                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {new Date(project.startDate).toLocaleDateString()} - {project.endDate ? new Date(project.endDate).toLocaleDateString() : 'Present'}
                          </p>

                          <p className="mt-3 text-sm text-gray-600 dark:text-gray-400 border-t border-gray-100 dark:border-gray-700 pt-3">
                            {project.description}
                          </p>

                          {project.technologies.length > 0 && (
                            <div className="flex flex-wrap gap-1.5 mt-3">
                              {project.technologies.map((tech, techIndex) => (
                                <span key={techIndex} className="px-2 py-0.5 text-xs bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                                  {tech}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>

                        <div className="flex md:flex-col gap-2 mt-2 md:mt-0">
                          <button
                            type="button"
                            onClick={() => handleRemoveProject(index)}
                            className="inline-flex items-center justify-center p-2 text-red-500 bg-red-50 rounded-full hover:bg-red-100 hover:text-red-600 dark:bg-red-900/20 dark:hover:bg-red-900/30"
                            aria-label="Remove project"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="mb-6">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add New Project
                </h5>
                <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                  <div className="lg:col-span-2">
                    <Label required>Project Title</Label>
                    <Input
                      type="text"
                      placeholder="Project Name"
                      value={newProject.title}
                      onChange={(e) => setNewProject({...newProject, title: e.target.value})}
                      error={errors.title}
                    />
                    {errors.title && (
                      <p className="mt-1 text-xs text-red-500">{errors.title}</p>
                    )}
                  </div>
                  <div className="lg:col-span-2">
                    <Label required>Description</Label>
                    <TextArea
                      rows={4}
                      placeholder="Describe your project"
                      value={newProject.description}
                      onChange={(value) => setNewProject({...newProject, description: value})}
                      error={errors.description}
                    />
                    {errors.description && (
                      <p className="mt-1 text-xs text-red-500">{errors.description}</p>
                    )}
                  </div>
                  <div className="lg:col-span-2">
                    <Label>Technologies Used</Label>
                    <div className="flex gap-2">
                      <Input
                        type="text"
                        placeholder="Add a technology"
                        value={techInput}
                        onChange={(e) => setTechInput(e.target.value)}
                      />
                      <Button
                        onClick={handleAddTech}
                        className="whitespace-nowrap inline-flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add
                      </Button>
                    </div>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {newProject.technologies.map((tech, index) => (
                        <div key={index} className="flex items-center gap-1 px-2 py-1 text-sm bg-gray-100 rounded-full dark:bg-gray-700 dark:text-gray-300">
                          {tech}
                          <button
                            type="button"
                            onClick={() => handleRemoveTech(tech)}
                            className="text-gray-500 hover:text-red-500"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="lg:col-span-2">
                    <Label>Project URL</Label>
                    <Input
                      type="url"
                      placeholder="https://github.com/yourusername/project"
                      value={newProject.url}
                      onChange={(e) => setNewProject({...newProject, url: e.target.value})}
                    />
                  </div>
                  <div>
                    <DatePicker
                      id="project-start-date"
                      label="Start Date"
                      required
                      placeholder="When did you start?"
                      date={newProject.startDate ? new Date(newProject.startDate) : undefined}
                      setDate={(date) => {
                        if (date) {
                          const dateStr = date.toISOString().split('T')[0];
                          setNewProject({...newProject, startDate: dateStr});
                        }
                      }}
                    />
                    {errors.startDate && (
                      <p className="mt-1 text-xs text-red-500">{errors.startDate}</p>
                    )}
                  </div>
                  <div>
                    <div className="flex items-center justify-between">
                      <Label required={!isOngoing}>End Date</Label>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="ongoing-project"
                          checked={isOngoing}
                          onChange={handleOngoingChange}
                          className="w-4 h-4 text-brand-500 border-gray-300 rounded focus:ring-brand-500"
                        />
                        <label htmlFor="ongoing-project" className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                          Ongoing Project
                        </label>
                      </div>
                    </div>
                    <div className={isOngoing ? "opacity-50 pointer-events-none" : ""}>
                      <DatePicker
                        id="project-end-date"
                        placeholder="When did you complete it?"
                        date={newProject.endDate ? new Date(newProject.endDate) : undefined}
                        setDate={(date) => {
                          if (date) {
                            const dateStr = date.toISOString().split('T')[0];
                            setNewProject({...newProject, endDate: dateStr});
                          }
                        }}
                      />
                    </div>
                    {errors.endDate && (
                      <p className="mt-1 text-xs text-red-500">{errors.endDate}</p>
                    )}
                  </div>
                </div>
                <div className="mt-6">
                  <Button
                    onClick={handleAddProject}
                    className="w-full bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Project
                  </Button>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end gap-4 px-2 mt-8">
              <Button
                variant="outline"
                onClick={closeModal}
                className="w-full lg:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="w-full lg:w-auto"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
