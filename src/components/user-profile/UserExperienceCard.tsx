"use client";
import React, { useState } from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import TextArea from "../form/input/TextArea";
import DatePicker from "../form/date-picker";

interface UserExperienceProps {
  userData?: any;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function UserExperienceCard({ userData }: UserExperienceProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const [experiences, setExperiences] = useState([
    {
      company: "Tech Innovations Inc.",
      role: "Senior Developer",
      location: "San Francisco, CA",
      description: "Led a team of developers to build scalable web applications using React and Node.js.",
      startDate: "2020-03-01",
      endDate: ""
    }
  ]);

  const [newExperience, setNewExperience] = useState({
    company: "",
    role: "",
    location: "",
    description: "",
    startDate: "",
    endDate: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isCurrentlyWorking, setIsCurrentlyWorking] = useState<boolean>(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!newExperience.company.trim()) {
      newErrors.company = "Company name is required";
    }

    if (!newExperience.role.trim()) {
      newErrors.role = "Role is required";
    }

    if (!newExperience.startDate) {
      newErrors.startDate = "Start date is required";
    }

    if (!isCurrentlyWorking && !newExperience.endDate) {
      newErrors.endDate = "End date is required if not currently working";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddExperience = (e?: React.MouseEvent) => {
    if (e) e.preventDefault();

    if (validateForm()) {
      // Create a copy of the experience object
      const experienceToAdd = { ...newExperience };

      // If currently working, set endDate to empty
      if (isCurrentlyWorking) {
        experienceToAdd.endDate = "";
      }

      setExperiences([...experiences, experienceToAdd]);
      setNewExperience({
        company: "",
        role: "",
        location: "",
        description: "",
        startDate: "",
        endDate: ""
      });
      setIsCurrentlyWorking(false);
      setErrors({});
    }
  };

  const handleCurrentlyWorkingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsCurrentlyWorking(e.target.checked);
    if (e.target.checked) {
      // Clear end date if currently working
      setNewExperience({...newExperience, endDate: ""});
      // Clear any end date error
      setErrors({...errors, endDate: ""});
    }
  };

  const handleRemoveExperience = (index: number) => {
    const updatedExperiences = [...experiences];
    updatedExperiences.splice(index, 1);
    setExperiences(updatedExperiences);
  };

  const handleSave = () => {
    // Handle save logic here
    console.log("Saving experience changes...", experiences);
    closeModal();
  };

  return (
    <>
      <div className="p-5 border border-gray-200 rounded-2xl bg-gradient-to-b from-gray-50 to-white shadow-sm dark:border-gray-800 dark:from-gray-900/50 dark:to-gray-900 lg:p-6">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div className="w-full">
            <div className="flex items-center justify-between mb-5">
              <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 flex items-center">
                <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
                Experience
              </h4>
              <button
                onClick={openModal}
                className="inline-flex items-center gap-2 text-sm font-medium text-brand-500 transition-colors hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 bg-white/80 dark:bg-gray-800/50 rounded-full px-3 py-1 shadow-sm"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-[14px] w-[14px]"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                Edit
              </button>
            </div>
            <div className="grid grid-cols-1 gap-4 mt-4">
              {experiences.map((exp, index) => (
                <div key={index} className="p-5 border border-gray-100 rounded-xl bg-white dark:bg-gray-800/50 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <h5 className="font-semibold text-gray-800 dark:text-white/90 text-lg">{exp.role}</h5>
                        <span className="px-2.5 py-0.5 bg-brand-50 text-brand-600 text-xs font-medium rounded-full dark:bg-brand-900/30 dark:text-brand-400">
                          {exp.endDate ? 'Past' : 'Current'}
                        </span>
                      </div>
                      <p className="text-brand-500 dark:text-brand-400 font-medium flex items-center">
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        {exp.company}
                      </p>
                      {exp.location && (
                        <p className="text-gray-500 dark:text-gray-400 text-sm flex items-center mt-1">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                          </svg>
                          {exp.location}
                        </p>
                      )}
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 flex items-center">
                        <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        {new Date(exp.startDate).toLocaleDateString()} - {exp.endDate ? new Date(exp.endDate).toLocaleDateString() : 'Present'}
                      </p>
                      <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-700">
                        <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">{exp.description}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              {experiences.length === 0 && (
                <div className="py-10 px-6 border border-dashed border-gray-200 rounded-xl dark:border-gray-700 flex flex-col items-center justify-center">
                  <svg className="w-10 h-10 text-gray-300 dark:text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">No experience information added yet.</p>
                  <button
                    onClick={openModal}
                    className="inline-flex items-center gap-1.5 rounded-md bg-indigo-500 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900 mt-3"
                  >
                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Experience
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
        <div className="relative w-full p-4 overflow-y-auto bg-white no-scrollbar rounded-3xl dark:bg-gray-900 lg:p-11">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
              {experiences.length > 0 ? 'Edit Experience' : 'Add Experience'}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {experiences.length > 0
                ? 'Update your work experience to keep your profile up-to-date.'
                : 'Add your work experience to showcase your professional background.'}
            </p>
          </div>
          <form className="flex flex-col">
            <div className="px-2 overflow-y-auto custom-scrollbar h-[450px]">
              <div className="mb-6">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                  </svg>
                  Your Experience
                </h5>
                {experiences.length === 0 ? (
                  <div className="p-6 border border-dashed border-gray-200 rounded-xl dark:border-gray-700 flex flex-col items-center justify-center mb-4">
                    <svg className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <p className="text-gray-500 dark:text-gray-400">No experience information added yet.</p>
                  </div>
                ) : (
                  experiences.map((exp, index) => (
                    <div key={index} className="p-5 mb-4 border border-gray-100 rounded-xl bg-white dark:bg-gray-800/50 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                      <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h6 className="font-semibold text-gray-800 dark:text-white/90 text-lg">{exp.role}</h6>
                              <p className="text-brand-500 dark:text-brand-400 font-medium">
                                {exp.company}
                              </p>
                              {exp.location && (
                                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                  <span className="inline-flex items-center">
                                    <svg className="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    {exp.location}
                                  </span>
                                </p>
                              )}
                            </div>
                            <div className="flex items-center">
                              <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${
                                exp.endDate ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              }`}>
                                {exp.endDate ? 'Past' : 'Current'}
                              </span>
                            </div>
                          </div>

                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {new Date(exp.startDate).toLocaleDateString()} - {exp.endDate ? new Date(exp.endDate).toLocaleDateString() : 'Present'}
                          </p>

                          {exp.description && (
                            <p className="mt-3 text-sm text-gray-600 dark:text-gray-400 border-t border-gray-100 dark:border-gray-700 pt-3">
                              {exp.description}
                            </p>
                          )}
                        </div>

                        <div className="flex md:flex-col gap-2 mt-2 md:mt-0">
                          <button
                            type="button"
                            onClick={() => handleRemoveExperience(index)}
                            className="inline-flex items-center justify-center p-2 text-red-500 bg-red-50 rounded-full hover:bg-red-100 hover:text-red-600 dark:bg-red-900/20 dark:hover:bg-red-900/30"
                            aria-label="Remove experience"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="mb-6">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add New Experience
                </h5>
                <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                  <div>
                    <Label required>Company</Label>
                    <Input
                      type="text"
                      placeholder="Company Name"
                      value={newExperience.company}
                      onChange={(e) => setNewExperience({...newExperience, company: e.target.value})}
                      error={errors.company}
                    />
                    {errors.company && (
                      <p className="mt-1 text-xs text-red-500">{errors.company}</p>
                    )}
                  </div>
                  <div>
                    <Label required>Role</Label>
                    <Input
                      type="text"
                      placeholder="Your Job Title"
                      value={newExperience.role}
                      onChange={(e) => setNewExperience({...newExperience, role: e.target.value})}
                      error={errors.role}
                    />
                    {errors.role && (
                      <p className="mt-1 text-xs text-red-500">{errors.role}</p>
                    )}
                  </div>
                  <div className="lg:col-span-2">
                    <Label>Location</Label>
                    <Input
                      type="text"
                      placeholder="City, State or Remote"
                      value={newExperience.location}
                      onChange={(e) => setNewExperience({...newExperience, location: e.target.value})}
                    />
                  </div>
                  <div className="lg:col-span-2">
                    <Label>Description</Label>
                    <TextArea
                      rows={4}
                      placeholder="Describe your responsibilities and achievements"
                      value={newExperience.description}
                      onChange={(value) => setNewExperience({...newExperience, description: value})}
                    />
                  </div>
                  <div>
                    <DatePicker
                      id="experience-start-date"
                      label="Start Date"
                      required
                      placeholder="When did you start?"
                      date={newExperience.startDate ? new Date(newExperience.startDate) : undefined}
                      setDate={(date) => {
                        if (date) {
                          const dateStr = date.toISOString().split('T')[0];
                          setNewExperience({...newExperience, startDate: dateStr});
                        }
                      }}
                    />
                    {errors.startDate && (
                      <p className="mt-1 text-xs text-red-500">{errors.startDate}</p>
                    )}
                  </div>
                  <div>
                    <div className="flex items-center justify-between">
                      <Label required={!isCurrentlyWorking}>End Date</Label>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="currently-working"
                          checked={isCurrentlyWorking}
                          onChange={handleCurrentlyWorkingChange}
                          className="w-4 h-4 text-brand-500 border-gray-300 rounded focus:ring-brand-500"
                        />
                        <label htmlFor="currently-working" className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                          Currently Working
                        </label>
                      </div>
                    </div>
                    <div className={isCurrentlyWorking ? "opacity-50 pointer-events-none" : ""}>
                      <DatePicker
                        id="experience-end-date"
                        placeholder="When did you leave?"
                        date={newExperience.endDate ? new Date(newExperience.endDate) : undefined}
                        setDate={(date) => {
                          if (date) {
                            const dateStr = date.toISOString().split('T')[0];
                            setNewExperience({...newExperience, endDate: dateStr});
                          }
                        }}
                      />
                    </div>
                    {errors.endDate && (
                      <p className="mt-1 text-xs text-red-500">{errors.endDate}</p>
                    )}
                  </div>
                </div>
                <div className="mt-6">
                  <Button
                    onClick={handleAddExperience}
                    className="w-full bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Experience
                  </Button>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end gap-4 px-2 mt-8">
              <Button
                variant="outline"
                onClick={closeModal}
                className="w-full lg:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="w-full lg:w-auto"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
