"use client";
import React, { useState } from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import Select from "../form/Select";
import DatePicker from "../form/date-picker";

// Define types based on the education schema
interface Education {
  _id?: string;
  userId?: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate?: string;
  createdAt?: string;
  updatedAt?: string;
}

interface UserEducationProps {
  userData?: any;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export default function UserEducationCard({ userData }: UserEducationProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const [educations, setEducations] = useState<Education[]>([
    {
      institution: "Stanford University",
      degree: "Bachelor of Science",
      field: "Computer Science",
      startDate: "2018-09-01",
      endDate: "2022-06-30"
    }
  ]);

  const [newEducation, setNewEducation] = useState<Education>({
    institution: "",
    degree: "",
    field: "",
    startDate: "",
    endDate: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isCurrentlyStudying, setIsCurrentlyStudying] = useState<boolean>(false);

  // Degree options
  const degreeOptions = [
    { value: "High School Diploma", label: "High School Diploma" },
    { value: "Associate's Degree", label: "Associate's Degree" },
    { value: "Bachelor's Degree", label: "Bachelor's Degree" },
    { value: "Bachelor of Arts", label: "Bachelor of Arts (BA)" },
    { value: "Bachelor of Science", label: "Bachelor of Science (BS/BSc)" },
    { value: "Bachelor of Engineering", label: "Bachelor of Engineering (BEng)" },
    { value: "Master's Degree", label: "Master's Degree" },
    { value: "Master of Arts", label: "Master of Arts (MA)" },
    { value: "Master of Science", label: "Master of Science (MS/MSc)" },
    { value: "Master of Business Administration", label: "Master of Business Administration (MBA)" },
    { value: "Doctor of Philosophy", label: "Doctor of Philosophy (PhD)" },
    { value: "Doctor of Medicine", label: "Doctor of Medicine (MD)" },
    { value: "Juris Doctor", label: "Juris Doctor (JD)" },
    { value: "Certificate", label: "Certificate" },
    { value: "Diploma", label: "Diploma" },
    { value: "Other", label: "Other" }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!newEducation.institution.trim()) {
      newErrors.institution = "Institution is required";
    }

    if (!newEducation.degree.trim()) {
      newErrors.degree = "Degree is required";
    }

    if (!newEducation.startDate) {
      newErrors.startDate = "Start date is required";
    }

    if (!isCurrentlyStudying && !newEducation.endDate) {
      newErrors.endDate = "End date is required if not currently studying";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddEducation = (e?: React.MouseEvent) => {
    if (e) e.preventDefault();

    if (validateForm()) {
      // Create a copy of the education object
      const educationToAdd = { ...newEducation };

      // If currently studying, set endDate to empty
      if (isCurrentlyStudying) {
        educationToAdd.endDate = "";
      }

      setEducations([...educations, educationToAdd]);
      setNewEducation({
        institution: "",
        degree: "",
        field: "",
        startDate: "",
        endDate: ""
      });
      setIsCurrentlyStudying(false);
      setErrors({});
    }
  };

  const handleRemoveEducation = (index: number) => {
    const updatedEducations = [...educations];
    updatedEducations.splice(index, 1);
    setEducations(updatedEducations);
  };

  const handleSave = () => {
    // Handle save logic here
    console.log("Saving education changes...", educations);
    closeModal();
  };

  const handleCurrentlyStudyingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsCurrentlyStudying(e.target.checked);
    if (e.target.checked) {
      // Clear end date if currently studying
      setNewEducation({...newEducation, endDate: ""});
      // Clear any end date error
      setErrors({...errors, endDate: ""});
    }
  };

  return (
    <>
      <div className="p-5 border border-gray-200 rounded-2xl bg-gradient-to-b from-gray-50 to-white shadow-sm dark:border-gray-800 dark:from-gray-900/50 dark:to-gray-900 lg:p-6">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div className="w-full">
            <div className="flex items-center justify-between mb-5">
              <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 flex items-center">
                <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222"></path>
                </svg>
                Education
              </h4>
              <button
                onClick={openModal}
                className="inline-flex items-center gap-2 text-sm font-medium text-brand-500 transition-colors hover:text-brand-600 dark:text-brand-400 dark:hover:text-brand-300 bg-white/80 dark:bg-gray-800/50 rounded-full px-3 py-1 shadow-sm"
              >
                <svg
                  stroke="currentColor"
                  fill="none"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-[14px] w-[14px]"
                  height="1em"
                  width="1em"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
                Edit
              </button>
            </div>
            <div className="grid grid-cols-1 gap-4 mt-4">
              {educations.map((edu, index) => (
                <div key={index} className="p-5 border border-gray-100 rounded-xl bg-white dark:bg-gray-800/50 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                    <div className="flex-1">
                      <div className="flex justify-between items-start">
                        <div>
                          <h5 className="font-semibold text-gray-800 dark:text-white/90 text-lg">{edu.institution}</h5>
                          <p className="text-brand-500 dark:text-brand-400 font-medium">
                            {edu.degree}{edu.field ? ` in ${edu.field}` : ''}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${
                            edu.endDate ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          }`}>
                            {edu.endDate ? 'Completed' : 'In Progress'}
                          </span>
                        </div>
                      </div>

                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                        <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                        {new Date(edu.startDate).toLocaleDateString()} - {edu.endDate ? new Date(edu.endDate).toLocaleDateString() : 'Present'}
                      </p>


                    </div>
                  </div>
                </div>
              ))}
              {educations.length === 0 && (
                <div className="py-10 px-6 border border-dashed border-gray-200 rounded-xl dark:border-gray-700 flex flex-col items-center justify-center">
                  <svg className="w-10 h-10 text-gray-300 dark:text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">No education information added yet.</p>
                  <button
                    onClick={openModal}
                    className="inline-flex items-center gap-1.5 rounded-md bg-indigo-500 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2 dark:focus:ring-offset-gray-900"
                  >
                    <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Education
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
        <div className="relative w-full p-4 overflow-y-auto bg-white no-scrollbar rounded-3xl dark:bg-gray-900 lg:p-11">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
              {educations.length > 0 ? 'Edit Education' : 'Add Education'}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {educations.length > 0
                ? 'Update your education details to keep your profile up-to-date.'
                : 'Add your education details to showcase your academic background.'}
            </p>
          </div>
          <form className="flex flex-col">
            <div className="px-2 overflow-y-auto custom-scrollbar h-[450px]">
              <div className="mb-6">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                  </svg>
                  Your Education
                </h5>

                {educations.length === 0 ? (
                  <div className="p-6 border border-dashed border-gray-200 rounded-xl dark:border-gray-700 flex flex-col items-center justify-center mb-4">
                    <svg className="w-12 h-12 text-gray-300 dark:text-gray-600 mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <p className="text-gray-500 dark:text-gray-400">No education information added yet.</p>
                  </div>
                ) : (
                  educations.map((edu, index) => (
                    <div key={index} className="p-5 mb-4 border border-gray-100 rounded-xl bg-white dark:bg-gray-800/50 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow duration-300">
                      <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-2">
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h6 className="font-semibold text-gray-800 dark:text-white/90 text-lg">{edu.institution}</h6>
                              <p className="text-brand-500 dark:text-brand-400 font-medium">
                                {edu.degree}{edu.field ? ` in ${edu.field}` : ''}
                              </p>
                            </div>
                            <div className="flex items-center">
                              <span className={`px-2.5 py-0.5 text-xs font-medium rounded-full ${
                                edu.endDate ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                              }`}>
                                {edu.endDate ? 'Completed' : 'In Progress'}
                              </span>
                            </div>
                          </div>

                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-2 flex items-center">
                            <svg className="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            {new Date(edu.startDate).toLocaleDateString()} - {edu.endDate ? new Date(edu.endDate).toLocaleDateString() : 'Present'}
                          </p>


                        </div>

                        <div className="flex md:flex-col gap-2 mt-2 md:mt-0">
                          <button
                            type="button"
                            onClick={() => handleRemoveEducation(index)}
                            className="inline-flex items-center justify-center p-2 text-red-500 bg-red-50 rounded-full hover:bg-red-100 hover:text-red-600 dark:bg-red-900/20 dark:hover:bg-red-900/30"
                            aria-label="Remove education"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>

              <div className="mb-6">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 flex items-center">
                  <svg className="w-5 h-5 mr-2 text-brand-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  Add New Education
                </h5>
                <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                  <div className="lg:col-span-2">
                    <Label required>Institution</Label>
                    <Input
                      type="text"
                      placeholder="University or School Name"
                      value={newEducation.institution}
                      onChange={(e) => setNewEducation({...newEducation, institution: e.target.value})}
                      error={errors.institution}
                    />
                    {errors.institution && (
                      <p className="mt-1 text-xs text-red-500">{errors.institution}</p>
                    )}
                  </div>

                  <div>
                    <Label required>Degree</Label>
                    <Select
                      options={degreeOptions}
                      value={newEducation.degree}
                      onChange={(value) => setNewEducation({...newEducation, degree: value})}
                      placeholder="Select a degree"
                      error={errors.degree}
                    />
                    {errors.degree && (
                      <p className="mt-1 text-xs text-red-500">{errors.degree}</p>
                    )}
                  </div>

                  <div>
                    <Label>Field of Study</Label>
                    <Input
                      type="text"
                      placeholder="Computer Science, Business, etc."
                      value={newEducation.field}
                      onChange={(e) => setNewEducation({...newEducation, field: e.target.value})}
                    />
                  </div>

                  <div>
                    <DatePicker
                      id="education-start-date"
                      label="Start Date"
                      required
                      placeholder="When did you start?"
                      date={newEducation.startDate ? new Date(newEducation.startDate) : undefined}
                      setDate={(date) => {
                        if (date) {
                          const dateStr = date.toISOString().split('T')[0];
                          setNewEducation({...newEducation, startDate: dateStr});
                        }
                      }}
                    />
                    {errors.startDate && (
                      <p className="mt-1 text-xs text-red-500">{errors.startDate}</p>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center justify-between">
                      <Label required={!isCurrentlyStudying}>End Date</Label>
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="currently-studying"
                          checked={isCurrentlyStudying}
                          onChange={handleCurrentlyStudyingChange}
                          className="w-4 h-4 text-brand-500 border-gray-300 rounded focus:ring-brand-500"
                        />
                        <label htmlFor="currently-studying" className="ml-2 text-xs text-gray-600 dark:text-gray-400">
                          Currently Studying
                        </label>
                      </div>
                    </div>
                    <div className={isCurrentlyStudying ? "opacity-50 pointer-events-none" : ""}>
                      <DatePicker
                        id="education-end-date"
                        placeholder="When did you finish?"
                        date={newEducation.endDate ? new Date(newEducation.endDate) : undefined}
                        setDate={(date) => {
                          if (date) {
                            const dateStr = date.toISOString().split('T')[0];
                            setNewEducation({...newEducation, endDate: dateStr});
                          }
                        }}
                      />
                    </div>
                    {errors.endDate && (
                      <p className="mt-1 text-xs text-red-500">{errors.endDate}</p>
                    )}
                  </div>


                </div>

                <div className="mt-6">
                  <Button
                    onClick={handleAddEducation}
                    className="w-full bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add Education
                  </Button>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end gap-4 px-2 mt-8">
              <Button
                variant="outline"
                onClick={closeModal}
                className="w-full lg:w-auto"
              >
                Cancel
              </Button>
              <Button
                onClick={handleSave}
                className="w-full lg:w-auto"
              >
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
