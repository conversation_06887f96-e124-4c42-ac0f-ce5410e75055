"use client";
import { SessionProvider } from "next-auth/react";
import { ThemeProvider } from '@/context/ThemeContext';
import { SidebarProvider } from '@/context/SidebarContext';
import { Toaster } from "@/components/ui/toast/toaster";
import AuthWrapper from "@/components/auth/AuthWrapper";
import { usePathname } from "next/navigation";

export default function Providers({
  children,
  session
}: {
  children: React.ReactNode;
  session?: any;
}) {
  const pathname = usePathname();

  // Check if the current path is a public path that doesn't require authentication
  const isPublicPath = [
    '/signin',
    '/signup',
    '/reset-password',
    '/auth/error'
  ].some(path => pathname?.startsWith(path));

  return (
    <SessionProvider session={session}>
      <ThemeProvider>
        <SidebarProvider>
          {isPublicPath ? (
            // Don't wrap public paths with AuthWrapper
            <>
              {children}
              <Toaster />
            </>
          ) : (
            // Wrap protected paths with AuthWrapper
            <AuthWrapper>
              {children}
              <Toaster />
            </AuthWrapper>
          )}
        </SidebarProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}

