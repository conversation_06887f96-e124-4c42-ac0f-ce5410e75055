"use client";
import { signOut } from "next-auth/react";

const ProfileMenu = () => {
  const handleSignOut = async () => {
    await signOut({ 
      callbackUrl: "/signin",
      redirect: true 
    });
  };

  return (
    <div className="profile-dropdown">
      <div className="profile-info">
        <img src={userAvatar} alt="Profile" />
        <span>{username}</span>
      </div>
      <div className="dropdown-menu">
        <button onClick={() => window.location.href = '/edit-profile'}>
          <i className="icon-edit"></i> Edit profile
        </button>
        <button onClick={() => window.location.href = '/account-settings'}>
          <i className="icon-settings"></i> Account settings
        </button>
        <button onClick={() => window.location.href = '/support'}>
          <i className="icon-help"></i> Support
        </button>
        <div className="divider"></div>
        <button onClick={handleSignOut} className="logout-btn">
          <i className="icon-logout"></i> Sign out
        </button>
      </div>
    </div>
  );
};

export default ProfileMenu;
