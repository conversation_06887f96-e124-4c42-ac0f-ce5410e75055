'use client';

import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';

interface TestCase {
  input: string;
  output: string;
  explanation?: string;
}

interface TestCaseDisplayProps {
  testCases: TestCase[];
  activeTestCase: number;
  setActiveTestCase: (index: number) => void;
}

const TestCaseDisplay: React.FC<TestCaseDisplayProps> = ({
  testCases,
  activeTestCase,
  setActiveTestCase,
}) => {
  const [expandedTestCase, setExpandedTestCase] = useState<number | null>(null);

  const toggleTestCase = (index: number) => {
    setExpandedTestCase(expandedTestCase === index ? null : index);
  };

  return (
    <div className="space-y-3">
      <div className="flex space-x-2 mb-3">
        {testCases.map((_, index) => (
          <button
            key={index}
            onClick={() => setActiveTestCase(index)}
            className={`px-3 py-1.5 text-sm font-medium rounded-md ${
              activeTestCase === index
                ? 'bg-blue-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            }`}
          >
            Case {index + 1}
          </button>
        ))}
      </div>

      {testCases.length > 0 && (
        <div className="rounded-md bg-gray-800 overflow-hidden">
          <div
            className="flex justify-between items-center p-3 cursor-pointer hover:bg-gray-700"
            onClick={() => toggleTestCase(activeTestCase)}
          >
            <span className="font-medium text-gray-300">
              Test Case {activeTestCase + 1}
            </span>
            {expandedTestCase === activeTestCase ? (
              <ChevronUpIcon className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronDownIcon className="h-4 w-4 text-gray-400" />
            )}
          </div>

          <div className={`px-3 pb-3 ${expandedTestCase === activeTestCase ? 'block' : 'hidden'}`}>
            <div className="mb-2">
              <span className="text-sm font-medium text-gray-300">Input:</span>
              <pre className="mt-1 rounded bg-gray-900 p-2 font-mono text-sm text-gray-300 overflow-x-auto">
                {testCases[activeTestCase].input}
              </pre>
            </div>
            <div className="mb-2">
              <span className="text-sm font-medium text-gray-300">Output:</span>
              <pre className="mt-1 rounded bg-gray-900 p-2 font-mono text-sm text-gray-300 overflow-x-auto">
                {testCases[activeTestCase].output}
              </pre>
            </div>
            {testCases[activeTestCase].explanation && (
              <div>
                <span className="text-sm font-medium text-gray-300">Explanation:</span>
                <div className="mt-1 rounded bg-gray-900 p-2 text-sm text-gray-300 overflow-x-auto">
                  {testCases[activeTestCase].explanation}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default TestCaseDisplay;
