'use client';

import { useState, useEffect } from 'react';
import { CheckCircleIcon, XCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';

interface SystemRequirements {
  node: boolean;
  python3: boolean;
  javac: boolean;
  java: boolean;
  'g++': boolean;
}

interface SystemRequirementsProps {
  onRequirementsChecked?: (requirements: SystemRequirements) => void;
}

export default function SystemRequirements({ onRequirementsChecked }: SystemRequirementsProps) {
  const [requirements, setRequirements] = useState<SystemRequirements | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkRequirements();
  }, []);

  const checkRequirements = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/system/requirements');
      const data = await response.json();

      if (data.success) {
        setRequirements(data.requirements);
        onRequirementsChecked?.(data.requirements);
      } else {
        setError(data.error || 'Failed to check system requirements');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to check system requirements');
    } finally {
      setLoading(false);
    }
  };

  const getLanguageSupport = () => {
    if (!requirements) return [];

    return [
      {
        language: 'JavaScript',
        supported: requirements.node,
        requirement: 'Node.js',
        description: 'Required for JavaScript execution'
      },
      {
        language: 'Python',
        supported: requirements.python3,
        requirement: 'Python 3',
        description: 'Required for Python execution'
      },
      {
        language: 'Java',
        supported: requirements.javac && requirements.java,
        requirement: 'JDK (javac + java)',
        description: 'Required for Java compilation and execution'
      },
      {
        language: 'C++',
        supported: requirements['g++'],
        requirement: 'GCC (g++)',
        description: 'Required for C++ compilation and execution'
      }
    ];
  };

  if (loading) {
    return (
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
        <div className="flex items-center">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-blue-500 border-t-transparent mr-3"></div>
          <span className="text-sm text-blue-700 dark:text-blue-300">Checking system requirements...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <XCircleIcon className="h-5 w-5 text-red-500 mr-3" />
          <div>
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">System Check Failed</h3>
            <p className="text-sm text-red-700 dark:text-red-300 mt-1">{error}</p>
            <button
              onClick={checkRequirements}
              className="text-sm text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-200 mt-2 underline"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    );
  }

  const languageSupport = getLanguageSupport();
  const supportedCount = languageSupport.filter(lang => lang.supported).length;
  const totalCount = languageSupport.length;

  return (
    <div className="bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900 dark:text-white">
          Compiler Support ({supportedCount}/{totalCount})
        </h3>
        <button
          onClick={checkRequirements}
          className="text-xs text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
        >
          Refresh
        </button>
      </div>

      <div className="space-y-2">
        {languageSupport.map((lang) => (
          <div key={lang.language} className="flex items-center justify-between">
            <div className="flex items-center">
              {lang.supported ? (
                <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
              ) : (
                <XCircleIcon className="h-4 w-4 text-red-500 mr-2" />
              )}
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {lang.language}
              </span>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {lang.requirement}
            </span>
          </div>
        ))}
      </div>

      {supportedCount < totalCount && (
        <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="h-4 w-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-gray-600 dark:text-gray-400">
              <p className="font-medium mb-1">Missing compilers detected</p>
              <p>Some languages may fall back to simulation mode. Install the missing compilers for full functionality.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 