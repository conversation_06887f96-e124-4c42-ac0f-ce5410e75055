'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDownIcon, CheckIcon, CodeBracketIcon } from '@heroicons/react/24/outline';

interface LanguageSelectorProps {
  selectedLanguage: string;
  onLanguageChange: (language: string) => void;
  languages: string[];
}

const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  selectedLanguage,
  onLanguageChange,
  languages
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get language icon based on language name
  const getLanguageIcon = (language: string) => {
    switch (language) {
      case 'JavaScript':
        return (
          <div className="flex items-center justify-center w-5 h-5 bg-yellow-400 text-black font-bold text-xs rounded border border-yellow-500">
            JS
          </div>
        );
      case 'Python':
        return (
          <div className="flex items-center justify-center w-5 h-5 bg-blue-500 text-white font-bold text-xs rounded border border-blue-600">
            PY
          </div>
        );
      case 'Java':
        return (
          <div className="flex items-center justify-center w-5 h-5 bg-orange-600 text-white font-bold text-xs rounded border border-orange-700">
            JV
          </div>
        );
      case 'C++':
        return (
          <div className="flex items-center justify-center w-5 h-5 bg-blue-700 text-white font-bold text-xs rounded border border-blue-800">
            C++
          </div>
        );
      default:
        return (
          <div className="flex items-center justify-center w-5 h-5 bg-gray-500 text-white font-bold text-xs rounded border border-gray-600">
            {language.substring(0, 2).toUpperCase()}
          </div>
        );
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
      >
        <CodeBracketIcon className="h-4 w-4" />
        <span className="hidden sm:inline">{selectedLanguage}</span>
        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-1 w-48 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg z-20">
          <div className="py-1">
            <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
              Programming Language
            </div>
            {languages.map((language) => (
              <button
                key={language}
                onClick={() => {
                  onLanguageChange(language);
                  setIsOpen(false);
                }}
                className={`flex items-center w-full px-3 py-2 text-sm transition-colors ${
                  selectedLanguage === language
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <span className="mr-3">{getLanguageIcon(language)}</span>
                <span className="flex-1 text-left">{language}</span>
                {selectedLanguage === language && (
                  <CheckIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
