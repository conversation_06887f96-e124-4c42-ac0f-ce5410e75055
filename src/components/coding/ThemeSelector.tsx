'use client';

import React, { useState, useRef, useEffect } from 'react';
import { CheckIcon, ChevronDownIcon, SwatchIcon } from '@heroicons/react/24/outline';

interface ThemeSelectorProps {
  selectedTheme: string;
  onThemeChange: (themeId: string) => void;
  themes: Record<string, string>;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  selectedTheme,
  onThemeChange,
  themes
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get the current theme name and icon
  const getThemeIcon = (themeId: string) => {
    return (
      <span 
        className="h-3 w-3 rounded-full mr-2 border border-gray-300 dark:border-gray-600"
        style={{
          backgroundColor:
            themeId === 'vs-light' ? '#FFFFFF' :
            themeId === 'github-dark' ? '#0D1117' :
            themeId === 'monokai' ? '#272822' :
            themeId === 'solarized-dark' ? '#002B36' :
            themeId === 'mockly-dark' ? '#1E1E2E' : '#1E1E2E'
        }}
      />
    );
  };

  const selectedThemeName = themes[selectedTheme] || 'Theme';

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
      >
        <SwatchIcon className="h-4 w-4" />
        <span className="hidden sm:inline">{selectedThemeName}</span>
        <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-1 w-48 rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg z-20">
          <div className="py-1">
            <div className="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 border-b border-gray-200 dark:border-gray-700">
              Editor Theme
            </div>
            {Object.entries(themes).map(([themeId, themeName]) => (
              <button
                key={themeId}
                onClick={() => {
                  onThemeChange(themeId);
                  setIsOpen(false);
                }}
                className={`flex items-center w-full px-3 py-2 text-sm transition-colors ${
                  selectedTheme === themeId
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                {getThemeIcon(themeId)}
                <span className="flex-1 text-left">{themeName}</span>
                {selectedTheme === themeId && (
                  <CheckIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeSelector;
