'use client';

import React, { useState } from 'react';
import { CheckI<PERSON>, XMarkIcon, ClockIcon, ServerIcon, ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import { CheckCircleIcon, XCircleIcon, ExclamationCircleIcon } from '@heroicons/react/24/outline';

interface TestResult {
  input: string;
  expectedOutput: string;
  actualOutput: string;
  status: string;
  statusCode: number;
  error?: string;
  executionTime?: number;
  memory?: number;
  explanation?: string;
}

interface ExecutionResultsProps {
  results: {
    status: string;
    statusCode?: number;
    input?: string;
    output?: string;
    expectedOutput?: string;
    error?: string;
    executionTime?: number;
    memory?: number;
    explanation?: string;
    testResults?: TestResult[];
  };
  isSubmission?: boolean;
}

const ExecutionResults: React.FC<ExecutionResultsProps> = ({ results, isSubmission = false }) => {
  const [expandedTestCase, setExpandedTestCase] = useState<number | null>(null);

  const hasTestResults = results.testResults && results.testResults.length > 0;
  const isSuccess = results.status === 'Accepted';
  const hasError = results.error && results.error.trim() !== '';

  const formatTime = (timeMs: number) => {
    if (timeMs < 1000) return `${timeMs}ms`;
    return `${(timeMs / 1000).toFixed(2)}s`;
  };

  const formatMemory = (memoryKb: number) => {
    if (memoryKb < 1024) return `${memoryKb}KB`;
    return `${(memoryKb / 1024).toFixed(2)}MB`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Accepted':
        return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-800 dark:text-green-200';
      case 'Wrong Answer':
      case 'Runtime Error':
      case 'Compilation Error':
        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';
      case 'Time Limit Exceeded':
        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';
      default:
        return 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Accepted':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'Wrong Answer':
      case 'Runtime Error':
      case 'Compilation Error':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'Time Limit Exceeded':
        return <ClockIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ExclamationCircleIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  const getTestStatusBadge = (status: string) => {
    switch (status) {
      case 'Accepted':
        return 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300';
      case 'Wrong Answer':
        return 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300';
      case 'Runtime Error':
      case 'Compilation Error':
        return 'bg-orange-100 text-orange-700 dark:bg-orange-900/30 dark:text-orange-300';
      case 'Time Limit Exceeded':
        return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-3">
      {/* Overall Status Header - LeetCode Style */}
      <div className={`rounded-lg border p-4 ${getStatusColor(results.status)}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {getStatusIcon(results.status)}
            <div>
              <h3 className="font-semibold text-lg">{results.status}</h3>
              {isSubmission && hasTestResults && (
                <p className="text-sm opacity-75 mt-0.5">
                  {results.testResults?.filter(t => t.status === 'Accepted').length || 0} / {results.testResults?.length || 0} test cases passed
                </p>
              )}
            </div>
          </div>

          {/* Performance Metrics */}
          {isSuccess && (results.executionTime || results.memory) && (
            <div className="flex items-center space-x-4 text-sm">
              {results.executionTime && (
                <div className="flex items-center space-x-1">
                  <ClockIcon className="h-4 w-4" />
                  <span>{formatTime(results.executionTime)}</span>
                </div>
              )}
              {results.memory && (
                <div className="flex items-center space-x-1">
                  <ServerIcon className="h-4 w-4" />
                  <span>{formatMemory(results.memory)}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Error Message */}
        {hasError && (
          <div className="mt-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800">
            <p className="font-medium text-red-700 dark:text-red-300 text-sm mb-2">Error Details:</p>
            <pre className="text-sm text-red-600 dark:text-red-400 whitespace-pre-wrap overflow-x-auto max-h-24 overflow-y-auto font-mono">
              {results.error}
            </pre>
          </div>
        )}
      </div>

      {/* Test Results - LeetCode Style */}
      {hasTestResults && (
        <div className="space-y-2">
          {/* Header with stats */}
          <div className="flex items-center justify-between">
            <h4 className="font-semibold text-gray-900 dark:text-white">
              Test Cases
            </h4>
            <div className="flex items-center space-x-2 text-sm">
              <span className="text-green-600 dark:text-green-400 font-medium">
                {results.testResults!.filter(t => t.status === 'Accepted').length} passed
              </span>
              <span className="text-gray-400">•</span>
              <span className="text-red-600 dark:text-red-400 font-medium">
                {results.testResults!.filter(t => t.status !== 'Accepted').length} failed
              </span>
            </div>
          </div>

          {/* Test case list */}
          <div className="space-y-2 max-h-64 overflow-y-auto custom-scrollbar">
            {results.testResults!.map((test, index) => {
              const isExpanded = expandedTestCase === index;
              const testPassed = test.status === 'Accepted';

              return (
                <div
                  key={index}
                  className="rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 overflow-hidden"
                >
                  {/* Test Case Header */}
                  <div
                    className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors"
                    onClick={() => setExpandedTestCase(isExpanded ? null : index)}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="font-medium text-gray-900 dark:text-white">
                        Test Case {index + 1}
                      </span>
                      <span className={`inline-flex rounded-full px-2.5 py-0.5 text-xs font-medium ${getTestStatusBadge(test.status)}`}>
                        {test.status}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      {test.executionTime && (
                        <span className="text-xs text-gray-500 dark:text-gray-400 font-mono">
                          {formatTime(test.executionTime)}
                        </span>
                      )}
                      <ChevronDownIcon 
                        className={`h-4 w-4 text-gray-400 transition-transform duration-200 ${
                          isExpanded ? 'rotate-180' : ''
                        }`} 
                      />
                    </div>
                  </div>

                  {/* Test Case Details */}
                  {isExpanded && (
                    <div className="border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50">
                      <div className="p-3 space-y-3">
                        <div className="grid grid-cols-1 gap-3">
                          {/* Input */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Input:
                            </label>
                            <div className="bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-600 p-2">
                              <pre className="text-xs font-mono text-gray-800 dark:text-gray-200 overflow-x-auto">
                                {test.input || 'No input'}
                              </pre>
                            </div>
                          </div>

                          {/* Expected Output */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Expected:
                            </label>
                            <div className="bg-white dark:bg-gray-800 rounded-md border border-gray-200 dark:border-gray-600 p-2">
                              <pre className="text-xs font-mono text-gray-800 dark:text-gray-200 overflow-x-auto">
                                {test.expectedOutput || 'No expected output'}
                              </pre>
                            </div>
                          </div>

                          {/* Actual Output */}
                          <div>
                            <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                              Your Output:
                            </label>
                            <div className={`rounded-md border p-2 ${
                              testPassed
                                ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                                : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                            }`}>
                              <pre className={`text-xs font-mono overflow-x-auto ${
                                testPassed
                                  ? 'text-green-800 dark:text-green-200'
                                  : 'text-red-800 dark:text-red-200'
                              }`}>
                                {test.actualOutput || 'No output'}
                              </pre>
                            </div>
                          </div>

                          {/* Error if any */}
                          {test.error && (
                            <div>
                              <label className="block text-xs font-medium text-red-700 dark:text-red-300 mb-1">
                                Error:
                              </label>
                              <div className="bg-red-50 dark:bg-red-900/20 rounded-md border border-red-200 dark:border-red-800 p-2">
                                <pre className="text-xs font-mono text-red-700 dark:text-red-300 overflow-x-auto">
                                  {test.error}
                                </pre>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Summary stats */}
          {isSubmission && (
            <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-4 gap-4 text-center text-sm">
                <div>
                  <div className="font-semibold text-lg text-gray-900 dark:text-white">
                    {results.testResults?.length || 0}
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">Total</div>
                </div>
                <div>
                  <div className="font-semibold text-lg text-green-600 dark:text-green-400">
                    {results.testResults?.filter(t => t.status === 'Accepted').length || 0}
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">Passed</div>
                </div>
                <div>
                  <div className="font-semibold text-lg text-red-600 dark:text-red-400">
                    {results.testResults?.filter(t => t.status !== 'Accepted').length || 0}
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">Failed</div>
                </div>
                <div>
                  <div className="font-semibold text-lg text-gray-900 dark:text-white">
                    {results.testResults?.length 
                      ? Math.round((results.testResults.filter(t => t.status === 'Accepted').length / results.testResults.length) * 100)
                      : 0}%
                  </div>
                  <div className="text-gray-600 dark:text-gray-400">Success</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Single test result for run code */}
      {!hasTestResults && results.output !== undefined && (
        <div className="space-y-3">
          <h4 className="font-semibold text-gray-900 dark:text-white">Output</h4>
          <div className="space-y-3">
            {results.input && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Input:
                </label>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3">
                  <pre className="text-sm font-mono text-gray-800 dark:text-gray-200 overflow-x-auto">
                    {results.input}
                  </pre>
                </div>
              </div>
            )}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Output:
              </label>
              <div className={`rounded-lg border p-3 ${
                isSuccess
                  ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              }`}>
                <pre className={`text-sm font-mono overflow-x-auto ${
                  isSuccess
                    ? 'text-green-800 dark:text-green-200'
                    : 'text-gray-800 dark:text-gray-200'
                }`}>
                  {results.output || 'No output'}
                </pre>
              </div>
            </div>
            {results.expectedOutput && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Expected:
                </label>
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-3">
                  <pre className="text-sm font-mono text-gray-800 dark:text-gray-200 overflow-x-auto">
                    {results.expectedOutput}
                  </pre>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ExecutionResults;
