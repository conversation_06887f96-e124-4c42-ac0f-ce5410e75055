'use client';

import React, { useRef, useEffect } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { editor, languages } from 'monaco-editor';

interface MonacoEditorProps {
  language: string;
  value: string;
  onChange: (value: string | undefined) => void;
  theme?: string;
  height?: string;
  options?: editor.IStandaloneEditorConstructionOptions;
  onMount?: (editor: editor.IStandaloneCodeEditor) => void;
}

const getLanguageId = (language: string): string => {
  const languageMap: Record<string, string> = {
    'JavaScript': 'javascript',
    'Python': 'python',
    'Java': 'java',
    'C++': 'cpp',
    'TypeScript': 'typescript',
  };

  return languageMap[language] || 'javascript';
};

// Configure code suggestions and IntelliSense for the editor
const configureEditorSuggestions = (monaco: Monaco, language: string) => {
  // Enable suggestions for all languages
  const languageId = getLanguageId(language);

  // Common suggestion settings for all languages
  monaco.languages.setLanguageConfiguration(languageId, {
    wordPattern: /(-?\d*\.\d\w*)|([^\`\~\!\@\#\%\^\&\*\(\)\-\=\+\[\{\]\}\\\|\;\:\'\"\,\.\<\>\/\?\s]+)/g,
    comments: {
      lineComment: '//',
      blockComment: ['/*', '*/'],
    },
    brackets: [
      ['{', '}'],
      ['[', ']'],
      ['(', ')'],
    ],
    autoClosingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '`', close: '`' },
    ],
    surroundingPairs: [
      { open: '{', close: '}' },
      { open: '[', close: ']' },
      { open: '(', close: ')' },
      { open: '"', close: '"' },
      { open: "'", close: "'" },
      { open: '`', close: '`' },
    ],
  });

  // Register custom completion providers based on language
  if (languageId === 'javascript') {
    registerJavaScriptCompletions(monaco);
  } else if (languageId === 'python') {
    registerPythonCompletions(monaco);
  } else if (languageId === 'java') {
    registerJavaCompletions(monaco);
  } else if (languageId === 'cpp') {
    registerCppCompletions(monaco);
  }
};

// JavaScript completions
const registerJavaScriptCompletions = (monaco: Monaco) => {
  monaco.languages.registerCompletionItemProvider('javascript', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn,
      };

      const suggestions = [
        // Common JavaScript methods and properties
        {
          label: 'function',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'function ${1:name}(${2:params}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Function declaration',
          range,
        },
        {
          label: 'arrow',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '(${1:params}) => {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Arrow function',
          range,
        },
        {
          label: 'forEach',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'forEach((${1:item}) => {\n\t${0}\n})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Array forEach method',
          range,
        },
        {
          label: 'map',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'map((${1:item}) => {\n\t${0}\n\treturn ${1:item};\n})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Array map method',
          range,
        },
        {
          label: 'filter',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'filter((${1:item}) => {\n\t${0}\n\treturn true;\n})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Array filter method',
          range,
        },
        {
          label: 'reduce',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'reduce((${1:accumulator}, ${2:current}) => {\n\t${0}\n\treturn ${1:accumulator};\n}, ${3:initialValue})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Array reduce method',
          range,
        },
        {
          label: 'console.log',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'console.log(${1:value})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Log to console',
          range,
        },
        {
          label: 'if',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (${1:condition}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If statement',
          range,
        },
        {
          label: 'ifelse',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (${1:condition}) {\n\t${2}\n} else {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If-else statement',
          range,
        },
        {
          label: 'for',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (let ${1:i} = 0; ${1:i} < ${2:array}.length; ${1:i}++) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For loop',
          range,
        },
        {
          label: 'forin',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (const ${1:key} in ${2:object}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For-in loop',
          range,
        },
        {
          label: 'forof',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (const ${1:item} of ${2:iterable}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For-of loop',
          range,
        },
      ];

      return { suggestions };
    },
  });
};

// Python completions
const registerPythonCompletions = (monaco: Monaco) => {
  monaco.languages.registerCompletionItemProvider('python', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn,
      };

      const suggestions = [
        // Common Python methods and properties
        {
          label: 'def',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'def ${1:function_name}(${2:parameters}):\n\t${0}\n\treturn',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Function definition',
          range,
        },
        {
          label: 'class',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'class ${1:ClassName}:\n\tdef __init__(self, ${2:parameters}):\n\t\t${0}\n',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Class definition',
          range,
        },
        {
          label: 'if',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if ${1:condition}:\n\t${0}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If statement',
          range,
        },
        {
          label: 'ifelse',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if ${1:condition}:\n\t${2}\nelse:\n\t${0}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If-else statement',
          range,
        },
        {
          label: 'for',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for ${1:item} in ${2:iterable}:\n\t${0}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For loop',
          range,
        },
        {
          label: 'while',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'while ${1:condition}:\n\t${0}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'While loop',
          range,
        },
        {
          label: 'try',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'try:\n\t${1}\nexcept ${2:Exception} as ${3:e}:\n\t${0}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Try-except block',
          range,
        },
        {
          label: 'print',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'print(${1:value})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Print to console',
          range,
        },
        {
          label: 'len',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'len(${1:iterable})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Get length of an iterable',
          range,
        },
        {
          label: 'range',
          kind: monaco.languages.CompletionItemKind.Function,
          insertText: 'range(${1:start}, ${2:stop}, ${3:step})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Generate a sequence of numbers',
          range,
        },
        {
          label: 'list',
          kind: monaco.languages.CompletionItemKind.Class,
          insertText: 'list(${1:iterable})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Create a list',
          range,
        },
        {
          label: 'dict',
          kind: monaco.languages.CompletionItemKind.Class,
          insertText: 'dict(${1:mapping})',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Create a dictionary',
          range,
        },
        {
          label: 'lambda',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'lambda ${1:x}: ${0}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Lambda function',
          range,
        },
      ];

      return { suggestions };
    },
  });
};

// Java completions
const registerJavaCompletions = (monaco: Monaco) => {
  monaco.languages.registerCompletionItemProvider('java', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn,
      };

      const suggestions = [
        // Common Java methods and properties
        {
          label: 'public class',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'public class ${1:ClassName} {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Public class definition',
          range,
        },
        {
          label: 'public static void main',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'public static void main(String[] args) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Main method',
          range,
        },
        {
          label: 'public',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'public ',
          documentation: 'Public access modifier',
          range,
        },
        {
          label: 'private',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'private ',
          documentation: 'Private access modifier',
          range,
        },
        {
          label: 'protected',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'protected ',
          documentation: 'Protected access modifier',
          range,
        },
        {
          label: 'static',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'static ',
          documentation: 'Static modifier',
          range,
        },
        {
          label: 'void',
          kind: monaco.languages.CompletionItemKind.Keyword,
          insertText: 'void ',
          documentation: 'Void return type',
          range,
        },
        {
          label: 'if',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (${1:condition}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If statement',
          range,
        },
        {
          label: 'ifelse',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (${1:condition}) {\n\t${2}\n} else {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If-else statement',
          range,
        },
        {
          label: 'for',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (int ${1:i} = 0; ${1:i} < ${2:array}.length; ${1:i}++) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For loop',
          range,
        },
        {
          label: 'foreach',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (${1:Type} ${2:item} : ${3:collection}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For-each loop',
          range,
        },
        {
          label: 'while',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'while (${1:condition}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'While loop',
          range,
        },
        {
          label: 'try',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'try {\n\t${1}\n} catch (${2:Exception} ${3:e}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Try-catch block',
          range,
        },
        {
          label: 'System.out.println',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'System.out.println(${1:value});',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Print to console',
          range,
        },
        {
          label: 'ArrayList',
          kind: monaco.languages.CompletionItemKind.Class,
          insertText: 'ArrayList<${1:Type}> ${2:name} = new ArrayList<>();',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Create an ArrayList',
          range,
        },
        {
          label: 'HashMap',
          kind: monaco.languages.CompletionItemKind.Class,
          insertText: 'HashMap<${1:KeyType}, ${2:ValueType}> ${3:name} = new HashMap<>();',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Create a HashMap',
          range,
        },
      ];

      return { suggestions };
    },
  });
};

// C++ completions
const registerCppCompletions = (monaco: Monaco) => {
  monaco.languages.registerCompletionItemProvider('cpp', {
    provideCompletionItems: (model, position) => {
      const word = model.getWordUntilPosition(position);
      const range = {
        startLineNumber: position.lineNumber,
        endLineNumber: position.lineNumber,
        startColumn: word.startColumn,
        endColumn: word.endColumn,
      };

      const suggestions = [
        // Common C++ methods and properties
        {
          label: 'class',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'class ${1:ClassName} {\npublic:\n\t${0}\n};',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Class definition',
          range,
        },
        {
          label: 'struct',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'struct ${1:StructName} {\n\t${0}\n};',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Struct definition',
          range,
        },
        {
          label: 'main',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'int main() {\n\t${0}\n\treturn 0;\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Main function',
          range,
        },
        {
          label: 'include',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: '#include <${1:iostream}>',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Include directive',
          range,
        },
        {
          label: 'vector',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'std::vector<${1:int}> ${2:vec};',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Vector declaration',
          range,
        },
        {
          label: 'map',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'std::map<${1:KeyType}, ${2:ValueType}> ${3:map};',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Map declaration',
          range,
        },
        {
          label: 'if',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (${1:condition}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If statement',
          range,
        },
        {
          label: 'ifelse',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'if (${1:condition}) {\n\t${2}\n} else {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'If-else statement',
          range,
        },
        {
          label: 'for',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (int ${1:i} = 0; ${1:i} < ${2:size}; ${1:i}++) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'For loop',
          range,
        },
        {
          label: 'foreach',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'for (auto& ${1:item} : ${2:collection}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Range-based for loop',
          range,
        },
        {
          label: 'while',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'while (${1:condition}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'While loop',
          range,
        },
        {
          label: 'try',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'try {\n\t${1}\n} catch (${2:std::exception}& ${3:e}) {\n\t${0}\n}',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Try-catch block',
          range,
        },
        {
          label: 'cout',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'std::cout << ${1:value} << std::endl;',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Print to console',
          range,
        },
        {
          label: 'cin',
          kind: monaco.languages.CompletionItemKind.Method,
          insertText: 'std::cin >> ${1:variable};',
          insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
          documentation: 'Read from console',
          range,
        },
        {
          label: 'using namespace std',
          kind: monaco.languages.CompletionItemKind.Snippet,
          insertText: 'using namespace std;',
          documentation: 'Using namespace std',
          range,
        },
      ];

      return { suggestions };
    },
  });
};

// Define available editor themes
export const editorThemes = {
  'vs-dark': 'VS Dark',
  'vs-light': 'VS Light',
  'github-dark': 'GitHub Dark',
  'monokai': 'Monokai',
  'solarized-dark': 'Solarized Dark',
  'mockly-dark': 'Mockly Dark',
};

const MonacoEditor: React.FC<MonacoEditorProps> = ({
  language,
  value,
  onChange,
  theme = 'mockly-dark',
  height = '100%',
  options = {},
  onMount,
}) => {
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);

  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor, monaco: Monaco) => {
    editorRef.current = editor;

    // Configure IntelliSense and suggestions
    configureEditorSuggestions(monaco, language);

    // Call onMount callback if provided
    if (onMount) {
      onMount(editor);
    }

    // Define custom themes

    // Mockly Dark Theme
    monaco.editor.defineTheme('mockly-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'function', foreground: 'DCDCAA' },
      ],
      colors: {
        'editor.background': '#1E1E2E',
        'editor.foreground': '#D4D4D4',
        'editorCursor.foreground': '#AEAFAD',
        'editor.lineHighlightBackground': '#2D2D3A',
        'editorLineNumber.foreground': '#858585',
        'editor.selectionBackground': '#264F78',
        'editor.inactiveSelectionBackground': '#3A3D41',
        'scrollbarSlider.background': '#3F3F5A',
        'scrollbarSlider.hoverBackground': '#4F4F6A',
        'scrollbarSlider.activeBackground': '#5F5F7A',
      }
    });

    // GitHub Dark Theme
    monaco.editor.defineTheme('github-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A737D' },
        { token: 'keyword', foreground: 'F97583' },
        { token: 'string', foreground: '9ECBFF' },
        { token: 'number', foreground: 'B392F0' },
        { token: 'function', foreground: 'B392F0' },
      ],
      colors: {
        'editor.background': '#0D1117',
        'editor.foreground': '#C9D1D9',
        'editorCursor.foreground': '#C9D1D9',
        'editor.lineHighlightBackground': '#161B22',
        'editorLineNumber.foreground': '#6E7681',
        'editor.selectionBackground': '#3B5070',
        'editor.inactiveSelectionBackground': '#3A3D41',
        'scrollbarSlider.background': '#30363D',
        'scrollbarSlider.hoverBackground': '#3B434B',
        'scrollbarSlider.activeBackground': '#454D59',
      }
    });

    // Monokai Theme
    monaco.editor.defineTheme('monokai', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '88846F' },
        { token: 'keyword', foreground: 'F92672' },
        { token: 'string', foreground: 'E6DB74' },
        { token: 'number', foreground: 'AE81FF' },
        { token: 'function', foreground: 'A6E22E' },
      ],
      colors: {
        'editor.background': '#272822',
        'editor.foreground': '#F8F8F2',
        'editorCursor.foreground': '#F8F8F2',
        'editor.lineHighlightBackground': '#3E3D32',
        'editorLineNumber.foreground': '#90908A',
        'editor.selectionBackground': '#49483E',
        'editor.inactiveSelectionBackground': '#3A3D41',
        'scrollbarSlider.background': '#414339',
        'scrollbarSlider.hoverBackground': '#4E5044',
        'scrollbarSlider.activeBackground': '#5A5C4F',
      }
    });

    // Solarized Dark Theme
    monaco.editor.defineTheme('solarized-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '657B83' },
        { token: 'keyword', foreground: 'CB4B16' },
        { token: 'string', foreground: '2AA198' },
        { token: 'number', foreground: 'D33682' },
        { token: 'function', foreground: '268BD2' },
      ],
      colors: {
        'editor.background': '#002B36',
        'editor.foreground': '#839496',
        'editorCursor.foreground': '#839496',
        'editor.lineHighlightBackground': '#073642',
        'editorLineNumber.foreground': '#657B83',
        'editor.selectionBackground': '#274642',
        'editor.inactiveSelectionBackground': '#243443',
        'scrollbarSlider.background': '#073642',
        'scrollbarSlider.hoverBackground': '#094554',
        'scrollbarSlider.activeBackground': '#0B5565',
      }
    });

    // VS Light Theme
    monaco.editor.defineTheme('vs-light', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '008000' },
        { token: 'keyword', foreground: '0000FF' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'function', foreground: '795E26' },
      ],
      colors: {
        'editor.background': '#FFFFFF',
        'editor.foreground': '#000000',
        'editorCursor.foreground': '#000000',
        'editor.lineHighlightBackground': '#F5F5F5',
        'editorLineNumber.foreground': '#237893',
        'editor.selectionBackground': '#ADD6FF',
        'editor.inactiveSelectionBackground': '#E5EBF1',
        'scrollbarSlider.background': '#DADADA',
        'scrollbarSlider.hoverBackground': '#CCCCCC',
        'scrollbarSlider.activeBackground': '#BBBBBB',
      }
    });

    // Set editor options
    editor.updateOptions({
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontSize: 14,
      fontFamily: 'Menlo, Monaco, "Courier New", monospace',
      lineNumbers: 'on',
      renderLineHighlight: 'all',
      cursorBlinking: 'smooth',
      cursorSmoothCaretAnimation: 'on',
      smoothScrolling: true,
      padding: { top: 10 },
      // Enable suggestions and IntelliSense
      quickSuggestions: {
        other: true,
        comments: true,
        strings: true,
      },
      suggestOnTriggerCharacters: true,
      acceptSuggestionOnEnter: 'on',
      tabCompletion: 'on',
      wordBasedSuggestions: 'on',
      parameterHints: {
        enabled: true,
      },
      snippetSuggestions: 'inline',
      formatOnType: true,
      formatOnPaste: true,
      ...options,
    });

    // Make sure the theme is applied
    console.log('Initial editor theme set to:', theme);
    monaco.editor.setTheme(theme);

    // Focus the editor
    editor.focus();
  };

  // Handle language change
  useEffect(() => {
    if (editorRef.current) {
      const model = editorRef.current.getModel();
      if (model) {
        const monaco = (window as any).monaco;
        if (monaco) {
          console.log(`Changing editor language to: ${language} (${getLanguageId(language)})`);
          monaco.editor.setModelLanguage(model, getLanguageId(language));

          // Reconfigure suggestions for the new language
          configureEditorSuggestions(monaco, language);

          // Force editor to refresh
          setTimeout(() => {
            if (editorRef.current) {
              editorRef.current.updateOptions({});
              editorRef.current.layout();
            }
          }, 50);
        }
      }
    }
  }, [language]);

  // Handle theme change
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any).monaco;
      if (monaco) {
        // Set the editor theme
        console.log('Changing editor theme to:', theme);
        monaco.editor.setTheme(theme);
      }
    }
  }, [theme]);

  return (
    <div className="custom-scrollbar h-full w-full">
      <Editor
        height={height}
        language={getLanguageId(language)}
        value={value}
        onChange={onChange}
        theme={theme}
        onMount={handleEditorDidMount}
        loading={
          <div className="flex items-center justify-center h-full bg-gray-900">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        }
        options={{
          minimap: { enabled: false },
          scrollBeyondLastLine: false,
          fontSize: 14,
          fontFamily: 'Menlo, Monaco, "Courier New", monospace',
          lineNumbers: 'on',
          renderLineHighlight: 'all',
          scrollbar: {
            useShadows: false,
            verticalHasArrows: false,
            horizontalHasArrows: false,
            vertical: 'visible',
            horizontal: 'visible',
            verticalScrollbarSize: 10,
            horizontalScrollbarSize: 10,
          },
          // Enable suggestions and IntelliSense
          quickSuggestions: {
            other: true,
            comments: true,
            strings: true,
          },
          suggestOnTriggerCharacters: true,
          acceptSuggestionOnEnter: 'on',
          tabCompletion: 'on',
          wordBasedSuggestions: 'on',
          parameterHints: {
            enabled: true,
          },
          snippetSuggestions: 'inline',
          formatOnType: true,
          formatOnPaste: true,
          ...options,
        }}
      />
    </div>
  );
};

export default MonacoEditor;
