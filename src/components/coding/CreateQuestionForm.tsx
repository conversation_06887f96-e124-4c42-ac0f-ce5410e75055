"use client";
import React, { useState } from "react";
import Form from "@/components/form/Form";
import Label from "@/components/form/Label";
import Input from "@/components/form/input/InputField";
import Select, { components } from 'react-select';
import MultiSelect from "@/components/form/MultiSelect";
import Button from "@/components/ui/button/Button";
import { useRouter } from "next/navigation";

const CreateQuestionForm = () => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    difficulty: "",
    topics: [] as string[],
  });
  const [error, setError] = useState("");

  const difficultyOptions = [
    { value: "Easy", label: "Easy" },
    { value: "Medium", label: "Medium" },
    { value: "Hard", label: "Hard" },
  ];

  const topicOptions = [
    { value: "arrays", text: "Arrays" },
    { value: "strings", text: "Strings" },
    { value: "linked-lists", text: "Linked Lists" },
    { value: "trees", text: "Trees" },
    { value: "graphs", text: "Graphs" },
    { value: "dynamic-programming", text: "Dynamic Programming" },
    { value: "sorting", text: "Sorting" },
    { value: "searching", text: "Searching" },
    { value: "recursion", text: "Recursion" },
    { value: "backtracking", text: "Backtracking" },
    { value: "greedy", text: "Greedy Algorithms" },
    { value: "bit-manipulation", text: "Bit Manipulation" },
    { value: "math", text: "Math" },
    { value: "design", text: "System Design" }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.description || !formData.difficulty || formData.topics.length === 0) {
      setError("Please fill in all required fields");
      return;
    }

    try {
      setIsSubmitting(true);
      setError("");
      
      const response = await fetch("/api/problems", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.message || "Failed to create problem");
      }
      
      // Redirect to problems list on success
      router.push("/coding/practice");
      router.refresh();
    } catch (error: any) {
      console.error("Error creating problem:", error);
      setError(error.message || "An error occurred while creating the problem");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <div className="rounded-md bg-red-50 p-4 dark:bg-red-900/20">
          <div className="flex">
            <div className="text-sm text-red-700 dark:text-red-400">
              {error}
            </div>
          </div>
        </div>
      )}
      
      <div>
        <Label htmlFor="title">Problem Title</Label>
        <Input
          id="title"
          type="text"
          placeholder="Enter problem title"
          onChange={(e) => setFormData({...formData, title: e.target.value})}
        />
      </div>
      
      <div>
        <Label htmlFor="description">Problem Description</Label>
        <textarea
          id="description"
          rows={8}
          className="w-full rounded-lg border border-gray-300 p-3 text-sm shadow-theme-xs placeholder:text-gray-400 focus:border-brand-300 focus:outline-hidden focus:ring-3 focus:ring-brand-500/10 dark:border-gray-700 dark:bg-gray-900 dark:text-white/90 dark:placeholder:text-white/30 dark:focus:border-brand-800"
          placeholder="Describe the problem, including examples and constraints"
          onChange={(e) => setFormData({...formData, description: e.target.value})}
        />
      </div>
      
      <div>
        <Label>Difficulty Level</Label>
        <div className="relative">
          <Select
            options={difficultyOptions}
            placeholder="Select difficulty"
            onChange={(value) => setFormData({...formData, difficulty: value})}
          />
          <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M4 6L8 10L12 6" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </span>
        </div>
      </div>
      
      <div>
        <label className="block text-sm font-medium mb-2 text-gray-700 dark:text-gray-300">
          Topics <span className="text-red-500 dark:text-red-400">*</span>
        </label>
        <Select 
          isMulti
          value={formData.topics.map(topic => {
            return { value: topic, label: topic };
          })}
          onChange={(options) => {
            setFormData({...formData, topics: options.map(option => option.value)});
          }}
          options={topicOptions.map(topic => ({ value: topic.value, label: topic.text }))}
          placeholder="Select topics"
          className="react-select-container"
          classNamePrefix="react-select"
          backspaceRemovesValue={true}
          onInputKeyDown={(e) => {
            // If backspace is pressed and input is empty, remove the last topic
            if (e.key === 'Backspace' && !e.currentTarget.value) {
              if (formData.topics.length > 0) {
                const newTopics = [...formData.topics];
                newTopics.pop();
                setFormData({...formData, topics: newTopics});
              }
            }
          }}
          styles={{
            control: (base, state) => ({
              ...base,
              background: 'var(--bg-color, #fff)',
              borderColor: state.isFocused ? '#6366F1' : 'var(--border-color, #E5E7EB)',
              boxShadow: state.isFocused ? '0 0 0 1px #6366F1' : 'none',
              '&:hover': {
                borderColor: '#6366F1'
              }
            }),
            input: (base) => ({
              ...base,
              color: 'var(--text-color, #111827)'
            }),
            placeholder: (base) => ({
              ...base,
              color: 'var(--placeholder-color, #6B7280)'
            }),
            menu: (base) => ({
              ...base,
              background: 'var(--dropdown-bg, #fff)',
              backdropFilter: 'blur(8px)',
              border: '1px solid var(--border-color, #E5E7EB)',
              borderRadius: '0.75rem',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
            }),
            option: (base, state) => ({
              ...base,
              backgroundColor: state.isSelected 
                ? 'var(--selected-bg, rgba(99, 102, 241, 0.1))'
                : state.isFocused
                ? 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
                : 'transparent',
              color: state.isSelected 
                ? 'var(--selected-text, #6366F1)'
                : 'var(--text-color, #111827)',
              '&:hover': {
                backgroundColor: 'var(--hover-bg, rgba(243, 244, 246, 0.8))'
              }
            }),
            multiValue: (base) => ({
              ...base,
              backgroundColor: 'var(--chip-bg, rgba(99, 102, 241, 0.1))',
              borderRadius: '0.5rem'
            }),
            multiValueLabel: (base) => ({
              ...base,
              color: 'var(--chip-text, #6366F1)',
              padding: '2px'
            }),
            multiValueRemove: (base) => ({
              ...base,
              color: 'var(--chip-remove, #6B7280)',
              ':hover': {
                backgroundColor: 'var(--chip-remove-hover-bg, rgba(99, 102, 241, 0.2))',
                color: 'var(--chip-remove-hover-text, #4F46E5)'
              }
            })
          }}
          components={{
            // Custom input component to handle backspace
            Input: (props) => {
              const { onKeyDown } = props;
              return (
                <components.Input
                  {...props}
                  onKeyDown={(e) => {
                    if (e.key === 'Backspace' && !e.currentTarget.value && formData.topics.length > 0) {
                      // Remove the last topic
                      const newTopics = [...formData.topics];
                      newTopics.pop();
                      setFormData({...formData, topics: newTopics});
                    }
                    // Call the original onKeyDown handler
                    if (onKeyDown) {
                      onKeyDown(e);
                    }
                  }}
                />
              );
            }
          }}
          theme={(theme) => ({
            ...theme,
            colors: {
              ...theme.colors,
              primary: '#6366F1',
              primary75: '#818CF8',
              primary50: '#EEF2FF',
              primary25: '#F5F3FF',
              neutral0: 'var(--bg-color, #fff)',
              neutral5: 'var(--neutral5, #F3F4F6)',
              neutral10: 'var(--neutral10, #F3F4F6)',
              neutral20: 'var(--neutral20, #E5E7EB)',
              neutral30: 'var(--neutral30, #D1D5DB)',
              neutral40: 'var(--neutral40, #9CA3AF)',
              neutral50: 'var(--neutral50, #6B7280)',
              neutral60: 'var(--neutral60, #4B5563)',
              neutral70: 'var(--neutral70, #374151)',
              neutral80: 'var(--neutral80, #1F2937)',
              neutral90: 'var(--neutral90, #111827)'
            }
          })}
        />
        <p className="text-xs text-gray-700 dark:text-gray-300 mt-1">Select at least one topic relevant to the problem</p>
        
        {/* Selected Topics Display */}
        {formData.topics.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {formData.topics.map(topic => (
              <div key={topic} 
                className="flex items-center bg-indigo-500/20 text-indigo-200 px-3 py-1.5 rounded-full border border-indigo-500/30"
              >
                <span>{topic}</span>
                <button 
                  onClick={() => setFormData({...formData, topics: formData.topics.filter(t => t !== topic)})}
                  className="ml-2 text-indigo-300 hover:text-indigo-100 transition-colors"
                  type="button"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      
      <div className="flex justify-end space-x-4 pt-4">
        <Button
          variant="outline"
          onClick={() => router.push("/coding/practice")}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting ? "Creating..." : "Create Problem"}
        </Button>
      </div>
    </Form>
  );
};

export default CreateQuestionForm;


