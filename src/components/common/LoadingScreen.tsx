'use client';

import React from 'react';
import Image from 'next/image';

const LoadingScreen: React.FC = () => {
  return (
    <div className="fixed inset-0 flex flex-col items-center justify-center bg-gray-950 z-50">
      <div className="relative w-24 h-24 mb-8">
        {/* You can replace this with your app logo */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-16 h-16 rounded-full border-4 border-blue-500/30 border-t-blue-500 animate-spin"></div>
        </div>
      </div>
      
      <h1 className="text-2xl font-bold text-white mb-2">Mockly</h1>
      <p className="text-gray-400">Loading your experience...</p>
    </div>
  );
};

export default LoadingScreen;
