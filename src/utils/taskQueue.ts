// Enhanced task queue with detailed logging
export class TaskQueue {
  private queue: Map<string, () => Promise<any>> = new Map();
  private processing: Set<string> = new Set();
  private results: Map<string, any> = new Map();
  private errors: Map<string, Error> = new Map();
  private isProcessing: boolean = false;

  // Add task to queue
  enqueue(taskId: string, task: () => Promise<any>): void {
    console.log(`[TaskQueue] Enqueuing task: ${taskId}`);
    this.queue.set(taskId, task);
    this.processQueue();
  }

  // Process queue items sequentially
  private async processQueue(): Promise<void> {
    if (this.isProcessing) return;
    
    this.isProcessing = true;
    console.log(`[TaskQueue] Starting queue processing. Tasks in queue: ${this.queue.size}`);
    
    while (this.queue.size > 0) {
      // Get first task
      const [taskId, task] = Array.from(this.queue.entries())[0];
      this.queue.delete(taskId);
      
      try {
        console.log(`[TaskQueue] Processing task: ${taskId}`);
        this.processing.add(taskId);
        
        // Execute task
        const result = await task();
        
        // Store result
        this.results.set(taskId, result);
        console.log(`[TaskQueue] Task completed successfully: ${taskId}`);
        
        // Dispatch event for UI updates
        const event = new CustomEvent('taskCompleted', { 
          detail: { taskId, result, success: true } 
        });
        window.dispatchEvent(event);
      } catch (error) {
        console.error(`[TaskQueue] Task failed: ${taskId}`, error);
        this.errors.set(taskId, error as Error);
        
        // Dispatch event for UI updates
        const event = new CustomEvent('taskCompleted', { 
          detail: { taskId, error, success: false } 
        });
        window.dispatchEvent(event);
      } finally {
        this.processing.delete(taskId);
      }
    }
    
    console.log('[TaskQueue] Queue processing complete');
    this.isProcessing = false;
  }

  // Get task status
  getStatus(taskId: string): 'queued' | 'processing' | 'completed' | 'error' | 'not-found' {
    if (this.queue.has(taskId)) return 'queued';
    if (this.processing.has(taskId)) return 'processing';
    if (this.results.has(taskId)) return 'completed';
    if (this.errors.has(taskId)) return 'error';
    return 'not-found';
  }

  // Get task result
  getResult(taskId: string): any {
    return this.results.get(taskId);
  }

  // Get task error
  getError(taskId: string): Error | undefined {
    return this.errors.get(taskId);
  }

  // Get all results
  getAllResults(): Map<string, any> {
    return new Map(this.results);
  }

  // Get queue stats
  getStats(): { queued: number, processing: number, completed: number, errors: number } {
    return {
      queued: this.queue.size,
      processing: this.processing.size,
      completed: this.results.size,
      errors: this.errors.size
    };
  }
}

// Create singleton instance
export const interviewTaskQueue = new TaskQueue();
