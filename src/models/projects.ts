  import mongoose, { Schema } from "mongoose";

const projectSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User" },
  title: { type: String, required: true },
  description: { type: String },
  technologies: [String],
  url: { type: String },
  startDate: { type: Date },
  endDate: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date }
}, {
  timestamps: true
});

projectSchema.index({ userId: 1 });

const Project = mongoose.models.Project || mongoose.model("Project", projectSchema);
export default Project;