import mongoose, { Schema } from "mongoose";

const interviewResponseSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
  sessionId: { type: Schema.Types.ObjectId, ref: "Session", required: true },
  questionId: { type: Schema.Types.ObjectId, ref: "Question", required: true },
  questionText: { type: String, required: true },
  
  // Speech-to-text results
  transcript: { type: String },
  
  // Response analysis results
  responseAnalysis: {
    content_quality: {
      relevance: { type: Number }, // 1-10 score
      completeness: { type: Number }, // 1-10 score
      accuracy: { type: Number }, // 1-10 score
      details: { type: String }
    },
    delivery_quality: {
      clarity: { type: String },
      pace: { type: Object },
      fillers: { type: Object },
      intonation: { type: Object }
    },
    recommended_response: {
      key_points: [String],
      structured_answer: { type: String },
      improvement_suggestions: [String]
    },
    overall_assessment: {
      technical_score: { type: Number }, // 1-10 score
      communication_score: { type: Number }, // 1-10 score
      overall_score: { type: Number }, // 1-10 score
      summary: { type: String },
      hiring_recommendation: { type: String }
    }
  },
  
  // Video analysis results
  videoAnalysis: {
    emotion_analysis: [{
      emotion: { type: String },
      frequency: { type: Number },
      average_score: { type: Number },
      confidence_level: { type: String }
    }],
    dominant_emotion: {
      emotion: { type: String },
      frequency: { type: Number },
      average_score: { type: Number },
      confidence_level: { type: String }
    },
    confidence_analysis: { type: Object },
    dominant_confidence: { type: String },
    confidence_summary: { type: String },
    analysis_summary: { type: String }
  },
  
  // Expiration settings
  createdAt: { type: Date, default: Date.now },
  expiresAt: { type: Date, default: () => new Date(Date.now() + 20 * 60 * 1000) } // 20 minutes from now
}, {
  timestamps: true
});

// Indexes for efficient querying
interviewResponseSchema.index({ userId: 1 });
interviewResponseSchema.index({ sessionId: 1 });
interviewResponseSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 }); // TTL index for auto-deletion

const InterviewResponse = mongoose.models.InterviewResponse || 
  mongoose.model("InterviewResponse", interviewResponseSchema);

export default InterviewResponse;