import mongoose, { Schema } from "mongoose";

// Create a pre-save hook to generate an ID if not provided
const problemSchema = new Schema({
  // Remove the _id field from the schema - MongoDB will generate it automatically
  title: { type: String, required: true },
  description: { type: String, required: true },
  difficulty: { type: String, required: true },
  topics: { type: [String], required: true }, // Array of topics with required validation
  constraints: { type: [String], required: true }, // Array of constraint strings with required validation
  testCases: { type: Array, default: [] }, // Array of test cases
  imageUrls: [String], // Array of image URLs for multiple images
  createdBy: { type: Schema.Types.ObjectId, ref: "User" },
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Add a pre-save hook to generate an ID if not provided
problemSchema.pre('save', function(next) {
  // If this is a new document (isNew is true) and no _id is set
  if (this.isNew && !this._id) {
    // Generate a new ObjectId and convert to string
    this._id = new mongoose.Types.ObjectId();
    console.log("Generated new _id for problem:", this._id);
  }
  next();
});

problemSchema.index({ difficulty: 1 });
problemSchema.index({ topics: 1 });

// Clear the model if it exists to avoid OverwriteModelError during development
if (mongoose.models.Problem) {
  delete mongoose.models.Problem;
}

const Problem = mongoose.model("Problem", problemSchema);
export default Problem;
