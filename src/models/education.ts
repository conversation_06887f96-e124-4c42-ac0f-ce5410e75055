import mongoose, { Schema } from "mongoose";

const educationSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User" },
  institution: { type: String, required: true },
  degree: { type: String, required: true },
  field: { type: String },
  startDate: { type: Date },
  endDate: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date }
}, {
  timestamps: true
});

educationSchema.index({ userId: 1 });

const Education = mongoose.models.Education || mongoose.model("Education", educationSchema);
export default Education;