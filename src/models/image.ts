import mongoose, { Schema } from "mongoose";

const imageSchema = new Schema({
  url: { type: String, required: true },
  publicId: { type: String, required: true },
  width: { type: Number },
  height: { type: Number },
  format: { type: String },
  folder: { type: String, default: 'uploads' },
  createdBy: { type: Schema.Types.ObjectId, ref: "User" },
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

// Create indexes for better query performance
imageSchema.index({ publicId: 1 });
imageSchema.index({ createdBy: 1 });
imageSchema.index({ folder: 1 });

// Clear the model if it exists to avoid OverwriteModelError during development
if (mongoose.models.Image) {
  delete mongoose.models.Image;
}

const Image = mongoose.model("Image", imageSchema);
export default Image;
