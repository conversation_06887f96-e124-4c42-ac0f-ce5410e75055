import mongoose, { Schema, models } from "mongoose";

const userSchema = new Schema({
  feedbackId: { type: Schema.Types.ObjectId, ref: "Feedback" },
  firstName: { type: String, required: true, maxlength: 50 },
  lastName: { type: String, required: false, maxlength: 50, default: '' },
  email: { type: String, required: true, unique: true },
  phone: { 
    type: String, 
    required: false, 
    maxlength: 15,
    default: undefined // Remove default null value
  },
  password: { type: String },
  userAvatar: { type: String },
  skills: [String],
  bio: { type: String, maxlength: 500 },
  educationId: { type: Schema.Types.ObjectId, ref: "Education" },
  projectId: { type: Schema.Types.ObjectId, ref: "Project" },
  experienceId: { type: Schema.Types.ObjectId, ref: "Experience" },
  provider: { type: String },
  role: { type: String, default: "user", enum: ["user", "admin"] },
  designation: { type: String },
  resume: { type: String },
  preferredLanguage: { type: String },
  lastLogin: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date },
  timezone: { type: String },
  location: { type: String },
  socialLinks: { type: Object },
  name: { type: String },
  image: { type: String },
});

// Pre-save middleware to update the updatedAt field
userSchema.pre("save", function (next) {
  this.updatedAt = new Date();

  // If firstName and lastName are provided but name isn't, generate name
  if (this.firstName && this.lastName && !this.name) {
    this.name = `${this.firstName} ${this.lastName}`;
  }

  next();
});

// Remove any existing indexes
if (mongoose.models.User) {
  delete mongoose.models.User;
}

const User = models.User || mongoose.model("User", userSchema);

export default User; 
