import mongoose, { Schema } from "mongoose";

const sessionSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
  role: { type: String, required: true },
  roundType: { type: String, required: true },
  duration: { type: Number, required: true },
  interviewer: { type: String },
  language: { type: String, required: true },
  resume: { type: String },
  termsAgreed: { type: Boolean, default: false },
  questions: [{ type: Schema.Types.ObjectId, ref: "Question" }],
  feedback: { type: Object, default: {} },
  downloadPdf: { type: String },
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true
});

sessionSchema.index({ userId: 1 });

const Session = mongoose.models.Session || mongoose.model("Session", sessionSchema);
export default Session;