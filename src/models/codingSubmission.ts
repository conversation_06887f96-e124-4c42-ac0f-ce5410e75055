import mongoose, { Schema } from "mongoose";

const codingSubmissionSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
  problemId: { type: String, required: true },
  code: { type: String, required: true },
  language: { type: String, required: true },
  results: { type: Object, default: {} },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date }
}, {
  timestamps: true
});

codingSubmissionSchema.index({ userId: 1 });
codingSubmissionSchema.index({ problemId: 1 });

const CodingSubmission = mongoose.models.CodingSubmission || mongoose.model("CodingSubmission", codingSubmissionSchema);
export default CodingSubmission;