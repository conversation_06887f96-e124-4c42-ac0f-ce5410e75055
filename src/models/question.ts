import mongoose, { Schema, Document } from "mongoose";

// Define an interface for the Question document
interface IQuestion extends Document {
  sessionId: mongoose.Types.ObjectId;
  kitId?: mongoose.Types.ObjectId;
  text: string;
  type: string;
  tags: string[];
  createdAt: Date;
}

const questionSchema = new Schema({
  sessionId: { type: Schema.Types.ObjectId, ref: "Session", required: true },
  kitId: { type: Schema.Types.ObjectId, ref: "CustomKit" },
  text: { type: String, required: true },
  type: { type: String, required: true },
  tags: [String],
  createdAt: { type: Date, default: Date.now }
}, {
  timestamps: true,
  strictQuery: false
});

questionSchema.index({ sessionId: 1 });
questionSchema.index({ kitId: 1 });

// Add post middleware to log errors
questionSchema.post('save', function(error: any, doc: any, next: any) {
  console.error('Question save error:', error);
  next(error);
});

questionSchema.post('insertMany', function(error: any, docs: any, next: any) {
  console.error('Question insertMany error:', error);
  next(error);
});

// Add a pre-save hook to log what's being saved
questionSchema.pre('save', function(next) {
  console.log('Saving question:', JSON.stringify(this));
  next();
});

// Add a pre-insertMany hook to log what's being inserted
questionSchema.pre('insertMany', function(next, docs) {
  console.log('Inserting questions:', JSON.stringify(docs).substring(0, 200) + '...');
  next();
});

// Make sure we're not redefining the model if it already exists
const Question = mongoose.models.Question || mongoose.model<IQuestion>("Question", questionSchema);
export default Question;
