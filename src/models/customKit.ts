import mongoose, { Schema } from "mongoose";

const customKitSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User", required: true },
  name: { type: String, required: true },
  questions: [{ type: Schema.Types.ObjectId, ref: "Question" }],
  settings: { type: Object, default: {} },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date }
}, {
  timestamps: true
});

customKitSchema.index({ userId: 1 });

const CustomKit = mongoose.models.CustomKit || mongoose.model("CustomKit", customKitSchema);
export default CustomKit;