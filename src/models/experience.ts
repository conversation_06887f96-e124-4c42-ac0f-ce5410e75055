import mongoose, { Schema } from "mongoose";

const experienceSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: "User" },
  company: { type: String, required: true },
  role: { type: String, required: true },
  location: { type: String },
  description: { type: String },
  startDate: { type: Date },
  endDate: { type: Date },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date }
}, {
  timestamps: true
});

experienceSchema.index({ userId: 1 });

const Experience = mongoose.models.Experience || mongoose.model("Experience", experienceSchema);
export default Experience;