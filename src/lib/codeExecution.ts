/**
 * Code Execution Service
 *
 * This service handles code execution for complete programs.
 * It supports JavaScript, Python, Java, and C++ with real compilation and execution.
 */

import { spawn } from 'child_process';
import { writeFileSync, unlinkSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { tmpdir } from 'os';

// Language IDs for Judge0 API (fallback)
export const LANGUAGE_IDS = {
  'JavaScript': 63,  // JavaScript (Node.js 12.14.0)
  'Python': 71,      // Python (3.8.1)
  'Java': 62,        // Java (OpenJDK 13.0.1)
  'C++': 54,         // C++ (GCC 9.2.0)
};

// Status codes
export const STATUS_CODES = {
  ACCEPTED: 3,
  WRONG_ANSWER: 4,
  TIME_LIMIT_EXCEEDED: 5,
  COMPILATION_ERROR: 6,
  RUNTIME_ERROR: 7,
  INTERNAL_ERROR: 8,
  EXEC_FORMAT_ERROR: 9,
  PENDING: 1,
  PROCESSING: 2,
};

// Status descriptions for user-friendly messages
export const STATUS_DESCRIPTIONS = {
  [STATUS_CODES.ACCEPTED]: 'Accepted',
  [STATUS_CODES.WRONG_ANSWER]: 'Wrong Answer',
  [STATUS_CODES.TIME_LIMIT_EXCEEDED]: 'Time Limit Exceeded',
  [STATUS_CODES.COMPILATION_ERROR]: 'Compilation Error',
  [STATUS_CODES.RUNTIME_ERROR]: 'Runtime Error',
  [STATUS_CODES.INTERNAL_ERROR]: 'Internal Error',
  [STATUS_CODES.EXEC_FORMAT_ERROR]: 'Execution Format Error',
  [STATUS_CODES.PENDING]: 'Pending',
  [STATUS_CODES.PROCESSING]: 'Processing',
};

// Create temp directory if it doesn't exist
const TEMP_DIR = join(tmpdir(), 'mockly-code-execution');
if (!existsSync(TEMP_DIR)) {
  mkdirSync(TEMP_DIR, { recursive: true });
}

// Generate unique filename
function generateUniqueFilename(extension: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2);
  return `code_${timestamp}_${random}.${extension}`;
}

// Execute code with timeout
function executeWithTimeout(command: string, args: string[], input: string, timeoutMs: number = 5000): Promise<{
  stdout: string;
  stderr: string;
  exitCode: number;
  timedOut: boolean;
}> {
  return new Promise((resolve) => {
    const process = spawn(command, args, {
      cwd: TEMP_DIR,
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let stdout = '';
    let stderr = '';
    let timedOut = false;

    // Set timeout
    const timeout = setTimeout(() => {
      timedOut = true;
      process.kill('SIGKILL');
    }, timeoutMs);

    // Handle stdout
    process.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    // Handle stderr
    process.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    // Handle process completion
    process.on('close', (exitCode) => {
      clearTimeout(timeout);
      resolve({
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode: exitCode || 0,
        timedOut
      });
    });

    // Handle process error
    process.on('error', (error) => {
      clearTimeout(timeout);
      resolve({
        stdout: '',
        stderr: error.message,
        exitCode: 1,
        timedOut: false
      });
    });

    // Send input to process
    if (input) {
      process.stdin.write(input);
      process.stdin.end();
    }
  });
}

// Execute JavaScript code
async function executeJavaScript(code: string, input: string) {
  const filename = generateUniqueFilename('js');
  const filepath = join(TEMP_DIR, filename);

  try {
    // Write code to file
    writeFileSync(filepath, code);

    // Execute with Node.js
    const result = await executeWithTimeout('node', [filepath], input, 5000);

    // Clean up
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }

    if (result.timedOut) {
      return {
        status: STATUS_CODES.TIME_LIMIT_EXCEEDED,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.TIME_LIMIT_EXCEEDED],
        output: '',
        error: 'Time limit exceeded (5000ms)',
        executionTime: 5.0,
        memory: null,
      };
    }

    // Check for syntax errors or compilation errors
    if (result.stderr && result.stderr.includes('SyntaxError')) {
      return {
        status: STATUS_CODES.COMPILATION_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.COMPILATION_ERROR],
        output: '',
        error: result.stderr,
        executionTime: null,
        memory: null,
      };
    }

    if (result.exitCode !== 0) {
      return {
        status: STATUS_CODES.RUNTIME_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.RUNTIME_ERROR],
        output: result.stdout,
        error: result.stderr || 'Runtime error occurred',
        executionTime: null,
        memory: null,
      };
    }

    return {
      status: STATUS_CODES.ACCEPTED,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.ACCEPTED],
      output: result.stdout,
      error: '',
      executionTime: 0.1,
      memory: 2048,
    };
  } catch (error) {
    // Clean up on error
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }

    return {
      status: STATUS_CODES.INTERNAL_ERROR,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.INTERNAL_ERROR],
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTime: null,
      memory: null,
    };
  }
}

// Execute Python code
async function executePython(code: string, input: string) {
  const filename = generateUniqueFilename('py');
  const filepath = join(TEMP_DIR, filename);

  try {
    // Write code to file
    writeFileSync(filepath, code);

    // Execute with Python
    const result = await executeWithTimeout('python3', [filepath], input, 5000);

    // Clean up
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }

    if (result.timedOut) {
      return {
        status: STATUS_CODES.TIME_LIMIT_EXCEEDED,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.TIME_LIMIT_EXCEEDED],
        output: '',
        error: 'Time limit exceeded (5000ms)',
        executionTime: 5.0,
        memory: null,
      };
    }

    // Check for syntax errors or compilation errors
    if (result.stderr && (result.stderr.includes('SyntaxError') || result.stderr.includes('IndentationError'))) {
      return {
        status: STATUS_CODES.COMPILATION_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.COMPILATION_ERROR],
        output: '',
        error: result.stderr,
        executionTime: null,
        memory: null,
      };
    }

    if (result.exitCode !== 0) {
      return {
        status: STATUS_CODES.RUNTIME_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.RUNTIME_ERROR],
        output: result.stdout,
        error: result.stderr || 'Runtime error occurred',
        executionTime: null,
        memory: null,
      };
    }

    return {
      status: STATUS_CODES.ACCEPTED,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.ACCEPTED],
      output: result.stdout,
      error: '',
      executionTime: 0.1,
      memory: 2048,
    };
  } catch (error) {
    // Clean up on error
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }

    return {
      status: STATUS_CODES.INTERNAL_ERROR,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.INTERNAL_ERROR],
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTime: null,
      memory: null,
    };
  }
}

// Execute Java code
async function executeJava(code: string, input: string) {
  const filename = generateUniqueFilename('java');
  const filepath = join(TEMP_DIR, filename);
  const classname = 'Solution';

  try {
    // Write code to file
    writeFileSync(filepath, code);

    // Compile Java code
    const compileResult = await executeWithTimeout('javac', [filepath], '', 10000);

    if (compileResult.exitCode !== 0) {
      // Clean up
      if (existsSync(filepath)) {
        unlinkSync(filepath);
      }

      return {
        status: STATUS_CODES.COMPILATION_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.COMPILATION_ERROR],
        output: '',
        error: compileResult.stderr || 'Compilation failed',
        executionTime: null,
        memory: null,
      };
    }

    // Execute compiled Java code
    const result = await executeWithTimeout('java', ['-cp', TEMP_DIR, classname], input, 5000);

    // Clean up
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }
    const classFilePath = join(TEMP_DIR, `${classname}.class`);
    if (existsSync(classFilePath)) {
      unlinkSync(classFilePath);
    }

    if (result.timedOut) {
      return {
        status: STATUS_CODES.TIME_LIMIT_EXCEEDED,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.TIME_LIMIT_EXCEEDED],
        output: '',
        error: 'Time limit exceeded (5000ms)',
        executionTime: 5.0,
        memory: null,
      };
    }

    if (result.exitCode !== 0) {
      return {
        status: STATUS_CODES.RUNTIME_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.RUNTIME_ERROR],
        output: result.stdout,
        error: result.stderr || 'Runtime error occurred',
        executionTime: null,
        memory: null,
      };
    }

    return {
      status: STATUS_CODES.ACCEPTED,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.ACCEPTED],
      output: result.stdout,
      error: '',
      executionTime: 0.2,
      memory: 4096,
    };
  } catch (error) {
    // Clean up on error
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }
    const classFilePath = join(TEMP_DIR, `${classname}.class`);
    if (existsSync(classFilePath)) {
      unlinkSync(classFilePath);
    }

    return {
      status: STATUS_CODES.INTERNAL_ERROR,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.INTERNAL_ERROR],
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTime: null,
      memory: null,
    };
  }
}

// Execute C++ code
async function executeCpp(code: string, input: string) {
  const filename = generateUniqueFilename('cpp');
  const filepath = join(TEMP_DIR, filename);
  const executablePath = join(TEMP_DIR, filename.replace('.cpp', ''));

  try {
    console.log(`C++: Writing code to ${filepath}`);
    console.log(`C++: Input data: "${input}"`);
    
    // Write code to file
    writeFileSync(filepath, code);

    // Compile C++ code with better flags
    const compileResult = await executeWithTimeout('g++', ['-std=c++17', '-o', executablePath, filepath], '', 10000);

    if (compileResult.exitCode !== 0) {
      console.log(`C++: Compilation failed with stderr: ${compileResult.stderr}`);
      // Clean up
      if (existsSync(filepath)) {
        unlinkSync(filepath);
      }

      return {
        status: STATUS_CODES.COMPILATION_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.COMPILATION_ERROR],
        output: '',
        error: compileResult.stderr || 'Compilation failed',
        executionTime: null,
        memory: null,
      };
    }

    console.log(`C++: Compilation successful, executing with input: "${input}"`);

    // Execute compiled C++ code
    const result = await executeWithTimeout(executablePath, [], input, 5000);

    console.log(`C++: Execution completed with exit code: ${result.exitCode}`);
    console.log(`C++: stdout: "${result.stdout}"`);
    console.log(`C++: stderr: "${result.stderr}"`);

    // Clean up
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }
    if (existsSync(executablePath)) {
      unlinkSync(executablePath);
    }

    if (result.timedOut) {
      return {
        status: STATUS_CODES.TIME_LIMIT_EXCEEDED,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.TIME_LIMIT_EXCEEDED],
        output: '',
        error: 'Time limit exceeded (5000ms)',
        executionTime: 5.0,
        memory: null,
      };
    }

    if (result.exitCode !== 0) {
      return {
        status: STATUS_CODES.RUNTIME_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.RUNTIME_ERROR],
        output: result.stdout,
        error: result.stderr || 'Runtime error occurred',
        executionTime: null,
        memory: null,
      };
    }

    return {
      status: STATUS_CODES.ACCEPTED,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.ACCEPTED],
      output: result.stdout,
      error: '',
      executionTime: 0.1,
      memory: 2048,
    };
  } catch (error) {
    console.error(`C++: Exception during execution: ${error}`);
    
    // Clean up on error
    if (existsSync(filepath)) {
      unlinkSync(filepath);
    }
    if (existsSync(executablePath)) {
      unlinkSync(executablePath);
    }

    return {
      status: STATUS_CODES.INTERNAL_ERROR,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.INTERNAL_ERROR],
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error',
      executionTime: null,
      memory: null,
    };
  }
}

// Main execution function
export async function executeCode(code: string, language: string, input: string) {
  try {
    console.log(`Executing ${language} code with input: ${input}`);

    // Check for empty code
    if (!code || code.trim() === '') {
      return {
        status: STATUS_CODES.COMPILATION_ERROR,
        statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.COMPILATION_ERROR],
        output: '',
        error: 'Code cannot be empty',
        executionTime: null,
        memory: null,
      };
    }

    // Execute based on language
    switch (language) {
      case 'JavaScript':
        return await executeJavaScript(code, input);
      case 'Python':
        return await executePython(code, input);
      case 'Java':
        return await executeJava(code, input);
      case 'C++':
        return await executeCpp(code, input);
      default:
        return {
          status: STATUS_CODES.INTERNAL_ERROR,
          statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.INTERNAL_ERROR],
          output: '',
          error: `Unsupported language: ${language}`,
          executionTime: null,
          memory: null,
        };
    }
  } catch (error) {
    console.error('Error executing code:', error);
    return {
      status: STATUS_CODES.INTERNAL_ERROR,
      statusDescription: STATUS_DESCRIPTIONS[STATUS_CODES.INTERNAL_ERROR],
      output: '',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      executionTime: null,
      memory: null,
    };
  }
}

// Check if required compilers/interpreters are available
export async function checkSystemRequirements() {
  const requirements = {
    node: false,
    python3: false,
    javac: false,
    java: false,
    'g++': false,
  };

  const commands = [
    { name: 'node', cmd: 'node', args: ['--version'] },
    { name: 'python3', cmd: 'python3', args: ['--version'] },
    { name: 'javac', cmd: 'javac', args: ['-version'] },
    { name: 'java', cmd: 'java', args: ['-version'] },
    { name: 'g++', cmd: 'g++', args: ['--version'] },
  ];

  for (const { name, cmd, args } of commands) {
    try {
      const result = await executeWithTimeout(cmd, args, '', 2000);
      requirements[name as keyof typeof requirements] = result.exitCode === 0 || result.stderr.includes('version');
    } catch (error) {
      requirements[name as keyof typeof requirements] = false;
    }
  }

  return requirements;
}
