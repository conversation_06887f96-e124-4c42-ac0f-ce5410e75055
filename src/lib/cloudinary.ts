import { v2 as cloudinary } from 'cloudinary';

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
  secure: true
});

/**
 * Upload a file to Cloudinary
 * @param file - The file buffer to upload
 * @param folder - Optional folder name to organize uploads
 * @returns Promise with the Cloudinary upload result
 */
export async function uploadToCloudinary(file: Buffer, folder: string = 'uploads'): Promise<any> {
  return new Promise((resolve, reject) => {
    // Create a readable stream from the buffer
    const stream = require('stream');
    const readableStream = new stream.Readable();
    readableStream.push(file);
    readableStream.push(null);

    // Create an upload stream to Cloudinary
    const uploadStream = cloudinary.uploader.upload_stream(
      {
        folder,
        resource_type: 'auto', // Automatically detect the resource type
      },
      (error, result) => {
        if (error) {
          return reject(error);
        }
        resolve(result);
      }
    );

    // Pipe the readable stream to the upload stream
    readableStream.pipe(uploadStream);
  });
}

/**
 * Upload a file to Cloudinary from a base64 string
 * @param base64String - The base64 string to upload
 * @param folder - Optional folder name to organize uploads
 * @returns Promise with the Cloudinary upload result
 */
export async function uploadBase64ToCloudinary(base64String: string, folder: string = 'uploads'): Promise<any> {
  return new Promise((resolve, reject) => {
    cloudinary.uploader.upload(
      base64String,
      {
        folder,
        resource_type: 'auto', // Automatically detect the resource type
      },
      (error, result) => {
        if (error) {
          return reject(error);
        }
        resolve(result);
      }
    );
  });
}

export default cloudinary;
