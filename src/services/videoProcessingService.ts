import { interviewTaskQueue } from '@/utils/taskQueue';

// Store for processing results
const processingResults: Record<string, any> = {};

// Save results to IndexedDB for persistence
export async function saveResultToIndexedDB(sessionId: string, result: any): Promise<void> {
  try {
    const db = await openResultsDatabase();
    const tx = db.transaction('results', 'readwrite');
    const store = tx.objectStore('results');
    
    // Add sessionId to the result object
    const resultWithSession = { ...result, sessionId };
    
    await store.put(resultWithSession);
    console.log(`[IndexedDB] Saved result for session ${sessionId}, question ${result.questionId}`);
  } catch (error) {
    console.error('[IndexedDB] Error saving result:', error);
  }
}

// Load results from IndexedDB
export async function loadResultsFromIndexedDB(sessionId: string): Promise<any[]> {
  try {
    const db = await openResultsDatabase();
    const tx = db.transaction('results', 'readonly');
    const store = tx.objectStore('results');
    const index = store.index('sessionId');
    
    return new Promise((resolve, reject) => {
      const request = index.getAll(sessionId);
      
      request.onsuccess = () => {
        const results = request.result;
        console.log(`[IndexedDB] Loaded ${results.length} results for session ${sessionId}`);
        resolve(results);
      };
      
      request.onerror = () => {
        console.error('[IndexedDB] Error loading results:', request.error);
        reject(request.error);
      };
    });
  } catch (error) {
    console.error('[IndexedDB] Error loading results:', error);
    return [];
  }
}

// Open IndexedDB database
async function openResultsDatabase(): Promise<IDBDatabase> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('interviewResults', 1);
    
    request.onupgradeneeded = (event) => {
      const db = request.result;
      if (!db.objectStoreNames.contains('results')) {
        const store = db.createObjectStore('results', { keyPath: 'questionId' });
        store.createIndex('sessionId', 'sessionId', { unique: false });
      }
    };
    
    request.onsuccess = () => resolve(request.result);
    request.onerror = () => reject(request.error);
  });
}

// Process a video recording
export async function processVideoRecording(
  sessionId: string,
  questionId: string,
  questionText: string,
  videoBlob: Blob
): Promise<string> {
  const taskId = `${sessionId}-${questionId}`;
  
  // Initialize result
  processingResults[taskId] = {
    questionId,
    questionText,
    status: 'pending',
    timestamp: new Date().toISOString()
  };
  
  // Ensure the blob has the correct MIME type
  const properVideoBlob = new Blob([videoBlob], { type: 'video/webm' });
  
  // Enqueue the processing task
  interviewTaskQueue.enqueue(taskId, async () => {
    try {
      // Update status
      processingResults[taskId].status = 'processing';
      console.log(`[Processing] Task ${taskId} started`);
      
      // Step 1: Process video for speech-to-text
      console.log('[Processing] Sending to speech-to-text API...');
      
      let speechData = { transcript: "" };
      let speechSuccess = false;
      
      try {
        // Create form data for speech-to-text
        const speechFormData = new FormData();
        speechFormData.append('file', properVideoBlob, `recording-${Date.now()}.webm`);
        
        const speechResponse = await fetch('http://localhost:8000/speech-to-text', {
          method: 'POST',
          body: speechFormData
        });
        
        if (!speechResponse.ok) {
          const errorText = await speechResponse.text();
          console.error('[Processing] Speech-to-text error:', errorText);
          throw new Error(`Speech-to-text failed: ${errorText}`);
        }
        
        speechData = await speechResponse.json();
        speechSuccess = true;
      } catch (error) {
        console.error('[Processing] Speech-to-text failed, using fallback empty transcript:', error);
        // Continue with empty transcript rather than failing the whole process
        speechData = { transcript: "[Transcription failed. Please enter your answer manually.]" };
      }
      
      processingResults[taskId].transcript = speechData.transcript;
      processingResults[taskId].answer = speechData.transcript;
      console.log('[Processing] Speech-to-text completed:', 
        speechData.transcript.substring(0, 50) + (speechData.transcript.length > 50 ? '...' : ''));
      
      // Step 2: Process video for emotion analysis
      console.log('[Processing] Sending to analyze-video API...');
      
      // Create a new FormData for video analysis
      const videoFormData = new FormData();
      videoFormData.append('video', properVideoBlob, `recording-${Date.now()}.webm`);
      
      const emotionResponse = await fetch('http://localhost:8000/analyze-video', {
        method: 'POST',
        body: videoFormData
      });
      
      if (!emotionResponse.ok) {
        const errorText = await emotionResponse.text();
        console.error('[Processing] Video analysis error:', errorText);
        throw new Error(`Video analysis failed: ${errorText}`);
      }
      
      const emotionData = await emotionResponse.json();
      processingResults[taskId].videoAnalysis = emotionData;
      console.log('[Processing] Video analysis completed:', 
        `Dominant emotion: ${emotionData.dominant_emotion?.emotion || 'Unknown'}`);
      
      // Step 3: Process transcript for response analysis
      if (speechData.transcript) {
        console.log('[Processing] Sending to response analysis API...');
        
        // Get session data from localStorage if available
        let jobProfile = 'Software Engineer';
        let experienceLevel = 'Mid';
        let requiredSkills: string[] = [];
        
        try {
          const sessionData = localStorage.getItem(`session-${sessionId}`);
          if (sessionData) {
            const parsed = JSON.parse(sessionData);
            jobProfile = parsed.role || jobProfile;
            experienceLevel = parsed.experienceLevel || experienceLevel;
            requiredSkills = parsed.skills || requiredSkills;
          }
        } catch (e) {
          console.warn('[Processing] Could not retrieve session data from localStorage:', e);
        }
        
        const analysisResponse = await fetch('http://localhost:8000/analyze', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            job_profile: jobProfile,
            question: questionText,
            answer: speechData.transcript,
            experience_level: experienceLevel,
            required_skills: requiredSkills
          })
        });
        
        if (!analysisResponse.ok) {
          const errorText = await analysisResponse.text();
          console.error('[Processing] Response analysis error:', errorText);
          throw new Error(`Response analysis failed: ${errorText}`);
        }
        
        const analysisData = await analysisResponse.json();
        processingResults[taskId].responseAnalysis = analysisData;
        
        // Extract feedback from analysis
        if (analysisData.analysis && analysisData.analysis.overall_assessment) {
          processingResults[taskId].feedback = analysisData.analysis.overall_assessment.summary;
          processingResults[taskId].score = analysisData.analysis.overall_assessment.overall_score;
        }
        
        console.log('[Processing] Response analysis completed');
      }
      
      // Update status
      processingResults[taskId].status = 'completed';
      processingResults[taskId].completedAt = new Date().toISOString();
      
      // Save to IndexedDB
      await saveResultToIndexedDB(sessionId, {
        ...processingResults[taskId],
        question: questionText
      });
      
      console.log(`[Processing] Task ${taskId} completed successfully`);
      return processingResults[taskId];
      
    } catch (error) {
      console.error(`[Processing] Task ${taskId} failed:`, error);
      processingResults[taskId].status = 'error';
      processingResults[taskId].error = error instanceof Error ? error.message : String(error);
      
      // Save error to IndexedDB
      await saveResultToIndexedDB(sessionId, {
        ...processingResults[taskId],
        question: questionText
      });
      
      throw error;
    }
  });
  
  return taskId;
}

// Get processing result for a specific task
export function getProcessingResult(taskId: string): any {
  return processingResults[taskId] || null;
}

// Get all processing results for a session
export function getSessionResults(sessionId: string): any[] {
  return Object.entries(processingResults)
    .filter(([key]) => key.startsWith(`${sessionId}-`))
    .map(([_, value]) => value);
}

// Check if all tasks for a session are complete
export function areAllTasksComplete(sessionId: string): boolean {
  const results = getSessionResults(sessionId);
  if (results.length === 0) return false;
  
  return results.every(result => 
    result.status === 'completed' || result.status === 'error');
}

// Get queue statistics for a session
export function getSessionQueueStats(sessionId: string): {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  error: number;
} {
  const results = getSessionResults(sessionId);
  
  return {
    total: results.length,
    pending: results.filter(r => r.status === 'pending').length,
    processing: results.filter(r => r.status === 'processing').length,
    completed: results.filter(r => r.status === 'completed').length,
    error: results.filter(r => r.status === 'error').length
  };
}

