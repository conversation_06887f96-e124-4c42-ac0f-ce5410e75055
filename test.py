from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor

# Download the model and processor
model_id = "openai/whisper-large-v3"
print(f"Downloading {model_id}...")

# Download model
model = AutoModelForSpeechSeq2Seq.from_pretrained(
    model_id,
    use_safetensors=True,
)
print("Model downloaded successfully")

# Download processor
processor = AutoProcessor.from_pretrained(model_id)
print("Processor downloaded successfully")

print("Whisper large-v3 model and processor downloaded successfully")