# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/.idea
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

project-documentation/
# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/.continue
venv/
# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

src/python/temp