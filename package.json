{"name": "free-nextjs-admin-dashboard", "version": "2.0.2", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.34.2", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@heroicons/react": "^2.2.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-toast": "^1.2.11", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.9", "@tailwindcss/postcss": "^4.0.9", "apexcharts": "^4.3.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "flatpickr": "^4.6.13", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.503.0", "mongoose": "^8.14.0", "next": "15.2.3", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-day-picker": "^9.6.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-select": "^5.10.1", "react-toastify": "^11.0.5", "swiper": "^11.2.0", "tailwind-merge": "^2.6.0", "vm2": "^3.9.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-transition-group": "^4.4.12", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "typescript": "^5"}, "overrides": {"@react-jvectormap/core": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}, "@react-jvectormap/world": {"react": "^16.8.0 || ^17 || ^18 || ^19", "react-dom": "^16.8.0 || ^17 || ^18 || ^19"}}}